import React from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../utils/cn';

export interface TableRowProps {
  children: React.ReactNode;
  selected?: boolean;
  hoverable?: boolean;
  clickable?: boolean;
  striped?: boolean;
  index?: number;
  onClick?: (event: React.MouseEvent<HTMLTableRowElement>) => void;
  className?: string;
  'data-testid'?: string;
}

export const TableRow: React.FC<TableRowProps> = ({
  children,
  selected = false,
  hoverable = true,
  clickable = false,
  striped = false,
  index = 0,
  onClick,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const isEven = index % 2 === 0;

  return (
    <tr
      className={cn(
        'border-b transition-colors',
        hoverable && 'hover:bg-opacity-50',
        clickable && 'cursor-pointer',
        selected && 'bg-blue-50 dark:bg-blue-900/20',
        striped && !isEven && 'bg-opacity-25',
        className
      )}
      style={{
        borderBottomColor: colors.border,
        backgroundColor: selected
          ? colors.primary + '10'
          : striped && !isEven
          ? colors.muted
          : 'transparent',
      }}
      onClick={onClick}
      data-testid={testId}
    >
      {children}
    </tr>
  );
};
