// Indicator Components
// Badges, chips, status indicators, and labels

// Badges
export { Badge } from './Badge';
export type { BadgeProps } from './Badge';

// Chips
export { Chip } from './Chip';
export type { ChipProps } from './Chip';

// Status Indicators
export { StatusIndicator } from './StatusIndicator';
export type { StatusIndicatorProps } from './StatusIndicator';

// TODO: Add more indicator components
// export { default as Tag } from './Tag';
// export { default as Label } from './Label';
