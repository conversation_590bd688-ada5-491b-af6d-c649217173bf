import React from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../utils/cn';

export interface InfoCardProps {
  title: string;
  description?: string;
  icon?: React.ReactNode;
  value?: string | number;
  variant?: 'default' | 'primary' | 'success' | 'warning' | 'error' | 'info';
  size?: 'sm' | 'md' | 'lg';
  hoverable?: boolean;
  clickable?: boolean;
  onClick?: (event: React.MouseEvent<HTMLDivElement>) => void;
  className?: string;
  'data-testid'?: string;
}

export const InfoCard: React.FC<InfoCardProps> = ({
  title,
  description,
  icon,
  value,
  variant = 'default',
  size = 'md',
  hoverable = false,
  clickable = false,
  onClick,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const sizeClasses = {
    sm: 'p-3',
    md: 'p-4',
    lg: 'p-6',
  };

  const titleSizes = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
  };

  const valueSizes = {
    sm: 'text-lg',
    md: 'text-xl',
    lg: 'text-2xl',
  };

  const variantStyles = {
    default: {
      backgroundColor: colors.surface,
      borderColor: colors.border,
      iconColor: colors.primary,
    },
    primary: {
      backgroundColor: colors.primary + '10',
      borderColor: colors.primary,
      iconColor: colors.primary,
    },
    success: {
      backgroundColor: '#10b98110',
      borderColor: '#10b981',
      iconColor: '#10b981',
    },
    warning: {
      backgroundColor: '#f59e0b10',
      borderColor: '#f59e0b',
      iconColor: '#f59e0b',
    },
    error: {
      backgroundColor: '#ef444410',
      borderColor: '#ef4444',
      iconColor: '#ef4444',
    },
    info: {
      backgroundColor: '#3b82f610',
      borderColor: '#3b82f6',
      iconColor: '#3b82f6',
    },
  };

  const currentStyle = variantStyles[variant];

  return (
    <div
      className={cn(
        'rounded-lg border transition-all duration-200',
        sizeClasses[size],
        hoverable && 'hover:shadow-md hover:scale-[1.02]',
        clickable && 'cursor-pointer',
        className
      )}
      style={{
        backgroundColor: currentStyle.backgroundColor,
        borderColor: currentStyle.borderColor,
      }}
      onClick={clickable ? onClick : undefined}
      data-testid={testId}
    >
      <div className="flex items-start space-x-3">
        {icon && (
          <div
            className="flex-shrink-0 p-2 rounded-lg"
            style={{
              backgroundColor: currentStyle.iconColor + '20',
              color: currentStyle.iconColor,
            }}
          >
            {icon}
          </div>
        )}
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <h3
              className={cn('font-medium truncate', titleSizes[size])}
              style={{ color: colors.text }}
            >
              {title}
            </h3>
            
            {value && (
              <div
                className={cn('font-bold', valueSizes[size])}
                style={{ color: currentStyle.iconColor }}
              >
                {value}
              </div>
            )}
          </div>
          
          {description && (
            <p
              className="mt-1 text-sm truncate"
              style={{ color: colors.textSecondary }}
            >
              {description}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};
