import React from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../utils/cn';

export interface ListItemProps {
  children: React.ReactNode;
  selected?: boolean;
  disabled?: boolean;
  clickable?: boolean;
  hoverable?: boolean;
  padding?: 'none' | 'sm' | 'md' | 'lg';
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
  onClick?: (event: React.MouseEvent<HTMLLIElement>) => void;
  className?: string;
  'data-testid'?: string;
}

export const ListItem: React.FC<ListItemProps> = ({
  children,
  selected = false,
  disabled = false,
  clickable = false,
  hoverable = true,
  padding = 'md',
  startIcon,
  endIcon,
  onClick,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const paddingClasses = {
    none: '',
    sm: 'px-2 py-1',
    md: 'px-4 py-2',
    lg: 'px-6 py-3',
  };

  return (
    <li
      className={cn(
        'flex items-center transition-colors',
        paddingClasses[padding],
        hoverable && !disabled && 'hover:bg-opacity-50',
        clickable && !disabled && 'cursor-pointer',
        selected && 'bg-blue-50 dark:bg-blue-900/20',
        disabled && 'opacity-50 cursor-not-allowed',
        className
      )}
      style={{
        backgroundColor: selected ? colors.primary + '10' : 'transparent',
        color: disabled ? colors.mutedForeground : colors.text,
      }}
      onClick={!disabled ? onClick : undefined}
      data-testid={testId}
    >
      {startIcon && (
        <div className="flex-shrink-0 mr-3">
          {startIcon}
        </div>
      )}
      
      <div className="flex-1 min-w-0">
        {children}
      </div>
      
      {endIcon && (
        <div className="flex-shrink-0 ml-3">
          {endIcon}
        </div>
      )}
    </li>
  );
};
