import React from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../utils/cn';

export interface BannerProps {
  title?: string;
  message: string;
  type?: 'info' | 'success' | 'warning' | 'error' | 'announcement';
  variant?: 'filled' | 'outlined' | 'subtle';
  size?: 'sm' | 'md' | 'lg';
  dismissible?: boolean;
  onDismiss?: () => void;
  icon?: React.ReactNode;
  actions?: React.ReactNode;
  fullWidth?: boolean;
  sticky?: boolean;
  className?: string;
  'data-testid'?: string;
}

/**
 * Banner component for prominent messages and announcements
 * Displays important information at the top of pages or sections
 */
export const Banner: React.FC<BannerProps> = ({
  title,
  message,
  type = 'info',
  variant = 'filled',
  size = 'md',
  dismissible = false,
  onDismiss,
  icon,
  actions,
  fullWidth = true,
  sticky = false,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const typeColors = {
    info: {
      primary: colors.primary,
      background: colors.primary + '10',
      border: colors.primary + '30',
    },
    success: {
      primary: '#10b981',
      background: '#10b981' + '10',
      border: '#10b981' + '30',
    },
    warning: {
      primary: '#f59e0b',
      background: '#f59e0b' + '10',
      border: '#f59e0b' + '30',
    },
    error: {
      primary: '#ef4444',
      background: '#ef4444' + '10',
      border: '#ef4444' + '30',
    },
    announcement: {
      primary: '#8b5cf6',
      background: '#8b5cf6' + '10',
      border: '#8b5cf6' + '30',
    },
  };

  const sizeClasses = {
    sm: 'px-4 py-2 text-sm',
    md: 'px-6 py-3 text-base',
    lg: 'px-8 py-4 text-lg',
  };

  const getVariantStyles = () => {
    const typeColor = typeColors[type];
    
    switch (variant) {
      case 'filled':
        return {
          backgroundColor: typeColor.primary,
          color: '#ffffff',
          border: 'none',
        };
      case 'outlined':
        return {
          backgroundColor: 'transparent',
          color: typeColor.primary,
          borderColor: typeColor.primary,
          borderWidth: '1px',
          borderStyle: 'solid',
        };
      case 'subtle':
        return {
          backgroundColor: typeColor.background,
          color: typeColor.primary,
          borderColor: typeColor.border,
          borderWidth: '1px',
          borderStyle: 'solid',
        };
      default:
        return {};
    }
  };

  const bannerClasses = cn(
    'flex items-start gap-3 rounded-lg transition-all duration-200',
    sizeClasses[size],
    fullWidth && 'w-full',
    sticky && 'sticky top-0 z-40',
    className
  );

  const variantStyles = getVariantStyles();

  return (
    <div
      className={bannerClasses}
      style={variantStyles}
      data-testid={testId}
    >
      {icon && (
        <div className="flex-shrink-0 mt-0.5">
          {icon}
        </div>
      )}
      
      <div className="flex-1 min-w-0">
        {title && (
          <h4 className="font-semibold mb-1">
            {title}
          </h4>
        )}
        <p className={cn('leading-relaxed', !title && 'font-medium')}>
          {message}
        </p>
      </div>

      {actions && (
        <div className="flex-shrink-0">
          {actions}
        </div>
      )}

      {dismissible && (
        <button
          onClick={onDismiss}
          className="flex-shrink-0 p-1 hover:opacity-70 transition-opacity"
          aria-label="Dismiss banner"
        >
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      )}
    </div>
  );
};
