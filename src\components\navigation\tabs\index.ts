// Tab Components
// Tabs, view switchers, and tabbed navigation

// View Mode Components
export { default as ViewModeSwitcher } from './ViewModeSwitcher';
export type { ViewModeSwitcherProps, ViewMode } from './ViewModeSwitcher';

export { default as ViewToggle } from './ViewToggle';
export type { ViewToggleProps } from './ViewToggle';

// Layout Tabs
export { default as Tabs } from './Tabs';
export type { TabsProps } from './Tabs';

// Basic Tabs
export { TabsBasic } from './TabsBasic';
export type { TabsBasicProps, TabItem } from './TabsBasic';

// Re-export from global for enhanced view mode selector
export {
  ViewModeSelector,
  dataViewModes,
  simpleViewModes,
  discussViewModes,
  tableViewModes
} from '../../global/ViewModeSelector';
export type {
  ViewModeSelectorProps,
  ViewModeOption,
  ViewModeVariant,
  ViewModeSize
} from '../../global/ViewModeSelector';

// TODO: Add more tab components
// export { default as TabPanel } from './TabPanel';
// export { default as TabList } from './TabList';
