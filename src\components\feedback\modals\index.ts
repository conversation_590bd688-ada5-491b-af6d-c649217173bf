// Modal Components
// Dialogs, modals, and popup windows

// Core Modals
export { default as Modal } from './Modal';
export type { ModalProps } from './Modal';

export { default as RelativeModal } from './RelativeModal';
export type { RelativeModalProps } from './RelativeModal';

// Specialized Modals
export { default as CustomerSupportModal } from './CustomerSupportModal';
export type { CustomerSupportModalProps } from './CustomerSupportModal';

// Dialog Components
export { ConfirmDialog } from './ConfirmDialog';
export type { ConfirmDialogProps } from './ConfirmDialog';

// TODO: Add more modal components
// export { default as AlertDialog } from './AlertDialog';
// export { default as FormModal } from './FormModal';
// export { default as FullscreenModal } from './FullscreenModal';
