// Navigation Components
export { default as AppNotFound } from './AppNotFound';
export type { AppNotFoundProps } from './AppNotFound';

export { default as Breadcrumb } from './Breadcrumb';
export type { BreadcrumbProps, BreadcrumbItem } from './Breadcrumb';

// Typography Components - moved from ui/
export { Text } from './Text';
export type { TextProps } from './Text';

export { Heading } from './Heading';
export type { HeadingProps } from './Heading';

// Layout Components - moved from ui/
export { Card } from './Card';
export type { CardProps } from './Card';

export { Separator } from './Separator';
export type { SeparatorProps } from './Separator';

// Unified View Mode Selector - replaces ViewModeSwitcher and ViewToggle
export { ViewModeSelector } from './ViewModeSelector';
export type {
  ViewModeSelectorProps,
  ViewModeOption,
  ViewModeVariant,
  ViewModeSize
} from './ViewModeSelector';
export {
  dataViewModes,
  simpleViewModes,
  discussViewModes,
  tableViewModes
} from './ViewModeSelector/presets';

// Enhanced Dropdown System - replaces ui/Dropdown and ui/FilterDropdown
export {
  Dropdown,
  FilterDropdown,
  DropdownBase,
  DropdownTrigger,
  DropdownContent,
  DropdownItem,
  DropdownSection,
  DropdownSeparator,
  useDropdownContext,
} from './Dropdown';
export type {
  DropdownProps,
  DropdownWithSectionsProps,
  BaseDropdownProps,
  DropdownItemType,
  DropdownSectionType,
  DropdownAlign,
  DropdownSize,
  DropdownVariant,
} from './Dropdown';

// ✅ Global Components Overview:
//
// These components are highly reusable and used across multiple modules.
// They follow strict design system principles and have stable APIs.
//
// Navigation Components:
// - AppNotFound: 404 error pages with customizable content
// - Breadcrumb: Navigation breadcrumbs with click handlers
//
// Typography Components:
// - Text: Enhanced text component with variants, sizes, and styling
// - Heading: Heading component with semantic levels and styling
//
// Layout Components:
// - Card: Flexible card container with variants and hover states
// - Separator: Visual separator with orientation and styling options
//
// View Mode Components:
// - ViewModeSelector: Unified view mode switching with presets
//   * Replaces deprecated ViewModeSwitcher and ViewToggle
//   * Includes presets for data views, simple views, discuss views, table views
//
// Dropdown System:
// - Dropdown: Basic dropdown with trigger and content
// - FilterDropdown: Specialized dropdown for filtering
// - DropdownBase: Base dropdown functionality
// - DropdownTrigger: Dropdown trigger component
// - DropdownContent: Dropdown content container
// - DropdownItem: Individual dropdown items
// - DropdownSection: Grouped dropdown sections
// - DropdownSeparator: Visual separators in dropdowns
// - useDropdownContext: Hook for dropdown state management
//
// Migration Notes:
// - Use these components instead of deprecated ui/ equivalents
// - Global components are optimized for performance and accessibility
// - All components support theming through useThemeStore
