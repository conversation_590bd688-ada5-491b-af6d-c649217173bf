import React from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../utils/cn';

export interface ListProps {
  children: React.ReactNode;
  variant?: 'default' | 'bordered' | 'divided' | 'minimal';
  size?: 'sm' | 'md' | 'lg';
  spacing?: 'none' | 'sm' | 'md' | 'lg';
  className?: string;
  'data-testid'?: string;
}

export const List: React.FC<ListProps> = ({
  children,
  variant = 'default',
  size = 'md',
  spacing = 'md',
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const sizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
  };

  const spacingClasses = {
    none: 'space-y-0',
    sm: 'space-y-1',
    md: 'space-y-2',
    lg: 'space-y-4',
  };

  const variantClasses = {
    default: '',
    bordered: 'border rounded-lg',
    divided: 'divide-y',
    minimal: '',
  };

  return (
    <ul
      className={cn(
        'list-none',
        sizeClasses[size],
        spacingClasses[spacing],
        variantClasses[variant],
        className
      )}
      style={{
        backgroundColor: variant === 'bordered' ? colors.surface : 'transparent',
        borderColor: colors.border,
      }}
      data-testid={testId}
    >
      {children}
    </ul>
  );
};
