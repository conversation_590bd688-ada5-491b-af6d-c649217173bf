import React, { useState, useEffect } from 'react';
import type { ResponsiveBreakpoint } from '../../../types/layout';

export interface BreakpointProps {
  children: React.ReactNode;
  show?: ResponsiveBreakpoint | ResponsiveBreakpoint[];
  hide?: ResponsiveBreakpoint | ResponsiveBreakpoint[];
}

const breakpointValues = {
  xs: 0,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
};

/**
 * Breakpoint component for conditional rendering based on screen size
 * Shows or hides content based on responsive breakpoints
 */
export const Breakpoint: React.FC<BreakpointProps> = ({
  children,
  show,
  hide,
}) => {
  const [currentBreakpoint, setCurrentBreakpoint] = useState<ResponsiveBreakpoint>('md');

  useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth;
      
      if (width >= breakpointValues['2xl']) {
        setCurrentBreakpoint('2xl');
      } else if (width >= breakpointValues.xl) {
        setCurrentBreakpoint('xl');
      } else if (width >= breakpointValues.lg) {
        setCurrentBreakpoint('lg');
      } else if (width >= breakpointValues.md) {
        setCurrentBreakpoint('md');
      } else if (width >= breakpointValues.sm) {
        setCurrentBreakpoint('sm');
      } else {
        setCurrentBreakpoint('xs');
      }
    };

    updateBreakpoint();
    window.addEventListener('resize', updateBreakpoint);
    
    return () => window.removeEventListener('resize', updateBreakpoint);
  }, []);

  const shouldShow = () => {
    if (hide) {
      const hideBreakpoints = Array.isArray(hide) ? hide : [hide];
      if (hideBreakpoints.includes(currentBreakpoint)) {
        return false;
      }
    }

    if (show) {
      const showBreakpoints = Array.isArray(show) ? show : [show];
      return showBreakpoints.includes(currentBreakpoint);
    }

    return true;
  };

  if (!shouldShow()) {
    return null;
  }

  return <>{children}</>;
};
