import React from 'react';
import { cn } from '../../../../utils/cn';
import type { GridLayoutProps } from '../../../types/layout';

/**
 * Grid component for CSS Grid layouts
 * Provides responsive grid properties and spacing controls
 */
export const Grid: React.FC<GridLayoutProps & { children: React.ReactNode }> = ({
  children,
  columns = 1,
  rows,
  autoFit = false,
  autoFill = false,
  minColumnWidth = '250px',
  gap,
  padding,
  margin,
  className = '',
  'data-testid': testId,
}) => {
  const gapClasses = {
    0: 'gap-0',
    1: 'gap-1',
    2: 'gap-2',
    3: 'gap-3',
    4: 'gap-4',
    5: 'gap-5',
    6: 'gap-6',
    8: 'gap-8',
    10: 'gap-10',
    12: 'gap-12',
    16: 'gap-16',
    20: 'gap-20',
    24: 'gap-24',
  };

  const paddingClasses = {
    0: 'p-0',
    1: 'p-1',
    2: 'p-2',
    3: 'p-3',
    4: 'p-4',
    5: 'p-5',
    6: 'p-6',
    8: 'p-8',
    10: 'p-10',
    12: 'p-12',
    16: 'p-16',
    20: 'p-20',
    24: 'p-24',
  };

  const marginClasses = {
    0: 'm-0',
    1: 'm-1',
    2: 'm-2',
    3: 'm-3',
    4: 'm-4',
    5: 'm-5',
    6: 'm-6',
    8: 'm-8',
    10: 'm-10',
    12: 'm-12',
    16: 'm-16',
    20: 'm-20',
    24: 'm-24',
  };

  const columnClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-2',
    3: 'grid-cols-3',
    4: 'grid-cols-4',
    5: 'grid-cols-5',
    6: 'grid-cols-6',
    7: 'grid-cols-7',
    8: 'grid-cols-8',
    9: 'grid-cols-9',
    10: 'grid-cols-10',
    11: 'grid-cols-11',
    12: 'grid-cols-12',
  };

  const rowClasses = {
    1: 'grid-rows-1',
    2: 'grid-rows-2',
    3: 'grid-rows-3',
    4: 'grid-rows-4',
    5: 'grid-rows-5',
    6: 'grid-rows-6',
  };

  const gridClasses = cn(
    'grid',
    typeof columns === 'number' && !autoFit && !autoFill && columnClasses[columns as keyof typeof columnClasses],
    typeof rows === 'number' && rowClasses[rows as keyof typeof rowClasses],
    typeof gap === 'number' && gapClasses[gap as keyof typeof gapClasses],
    typeof padding === 'number' && paddingClasses[padding as keyof typeof paddingClasses],
    typeof margin === 'number' && marginClasses[margin as keyof typeof marginClasses],
    className
  );

  const gridStyle: React.CSSProperties = {};

  if (autoFit) {
    gridStyle.gridTemplateColumns = `repeat(auto-fit, minmax(${minColumnWidth}, 1fr))`;
  } else if (autoFill) {
    gridStyle.gridTemplateColumns = `repeat(auto-fill, minmax(${minColumnWidth}, 1fr))`;
  }

  return (
    <div
      className={gridClasses}
      style={gridStyle}
      data-testid={testId}
    >
      {children}
    </div>
  );
};
