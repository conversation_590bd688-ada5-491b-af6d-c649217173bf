import React from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../utils/cn';

export interface TableHeaderProps {
  children: React.ReactNode;
  sortable?: boolean;
  sortDirection?: 'asc' | 'desc' | null;
  onSort?: () => void;
  align?: 'left' | 'center' | 'right';
  width?: string | number;
  className?: string;
  'data-testid'?: string;
}

export const TableHeader: React.FC<TableHeaderProps> = ({
  children,
  sortable = false,
  sortDirection = null,
  onSort,
  align = 'left',
  width,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const alignClasses = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right',
  };

  const getSortIcon = () => {
    if (!sortable) return null;
    
    if (sortDirection === 'asc') {
      return <span className="ml-1">↑</span>;
    } else if (sortDirection === 'desc') {
      return <span className="ml-1">↓</span>;
    } else {
      return <span className="ml-1 opacity-50">↕</span>;
    }
  };

  return (
    <th
      className={cn(
        'px-4 py-3 font-medium border-b',
        alignClasses[align],
        sortable && 'cursor-pointer hover:bg-opacity-50 select-none',
        className
      )}
      style={{
        backgroundColor: colors.muted,
        color: colors.mutedForeground,
        borderBottomColor: colors.border,
        width: width,
      }}
      onClick={sortable ? onSort : undefined}
      data-testid={testId}
    >
      <div className="flex items-center justify-between">
        <span>{children}</span>
        {getSortIcon()}
      </div>
    </th>
  );
};
