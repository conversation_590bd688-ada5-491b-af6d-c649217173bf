// Views Components - All view types for data display and interaction
// These components provide different ways to visualize and interact with data
//
// 🎯 View System Overview:
// A comprehensive collection of 12+ view components for enterprise applications.
// Each view provides unique ways to display and interact with data.
//
// 📊 Data Display Views:
// - ListView: Tabular data with sorting, filtering, and selection
// - GridView: Card-based responsive grid layout
// - CardView: Individual card displays with actions
//
// 📈 Data Visualization Views:
// - KanbanView: Kanban board for workflow management
// - CalendarView: Calendar display with event management
// - PivotView: Pivot table for data analysis
// - GraphView: Network graph visualization
//
// 📋 Activity and Timeline Views:
// - ActivityView: Activity streams and feeds
//
// 🗺️ Spatial and Hierarchical Views:
// - MapView: Geographic data visualization
// - GanttView: Project timeline and scheduling
// - HierarchicalView: Tree and hierarchical data
//
// 🔗 Relationship and Form Views:
// - RelationshipView: Entity relationships
// - FormView: Single record editing and display

// Base Views
export * from './BaseView';
export * from './ListView';
export * from './GridView';
export * from './CardView';

// Data Visualization Views
export * from './KanbanView';
export * from './CalendarView';
export * from './PivotView';
export * from './GraphView';

// Activity and Timeline Views
export * from './ActivityView';

// Spatial and Hierarchical Views
export * from './MapView';
export * from './GanttView';
export * from './HierarchicalView';

// Relationship and Form Views
export * from './RelationshipView';
export * from './FormView';

// View Types and Configurations
export * from '../../types/views';

// View Store
export { useViewStore } from '../../stores/viewStore';
export type { ViewStoreState } from '../../stores/viewStore';
