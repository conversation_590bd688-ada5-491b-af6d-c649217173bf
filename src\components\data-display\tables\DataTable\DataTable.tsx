import React, { useState, useMemo } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../utils/cn';
import { Table } from '../Table';
import { TableHeader } from '../TableHeader';
import { TableRow } from '../TableRow';
import { TableCell } from '../TableCell';

export interface DataTableColumn {
  key: string;
  label: string;
  width?: string | number;
  sortable?: boolean;
  render?: (value: any, item: any, index: number) => React.ReactNode;
  align?: 'left' | 'center' | 'right';
  className?: string;
}

export interface DataTableProps {
  data: any[];
  columns: DataTableColumn[];
  loading?: boolean;
  error?: string;
  showHeader?: boolean;
  striped?: boolean;
  hoverable?: boolean;
  selectable?: boolean;
  selectedItems?: string[];
  rowKey?: string | ((item: any) => string);
  onSort?: (column: string, direction: 'asc' | 'desc') => void;
  sortColumn?: string;
  sortDirection?: 'asc' | 'desc';
  onRowClick?: (item: any, index: number) => void;
  onRowSelect?: (item: any, selected: boolean) => void;
  renderRowActions?: (item: any, index: number) => React.ReactNode;
  emptyMessage?: string;
  className?: string;
  'data-testid'?: string;
}

export const DataTable: React.FC<DataTableProps> = ({
  data,
  columns,
  loading = false,
  error,
  showHeader = true,
  striped = true,
  hoverable = true,
  selectable = false,
  selectedItems = [],
  rowKey = 'id',
  onSort,
  sortColumn,
  sortDirection,
  onRowClick,
  onRowSelect,
  renderRowActions,
  emptyMessage = 'No data available',
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const getRowKey = (item: any, index: number): string => {
    if (typeof rowKey === 'function') {
      return rowKey(item);
    }
    return item[rowKey] || index.toString();
  };

  const isSelected = (item: any): boolean => {
    const key = getRowKey(item, 0);
    return selectedItems.includes(key);
  };

  const handleSort = (column: DataTableColumn) => {
    if (!column.sortable || !onSort) return;
    
    const newDirection = 
      sortColumn === column.key && sortDirection === 'asc' ? 'desc' : 'asc';
    onSort(column.key, newDirection);
  };

  const handleRowSelect = (
    item: any,
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    if (onRowSelect) {
      onRowSelect(item, event.target.checked);
    }
  };

  const renderCell = (column: DataTableColumn, item: any, index: number) => {
    const value = item[column.key];
    
    if (column.render) {
      return column.render(value, item, index);
    }
    
    return value;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8" data-testid={testId}>
        <div className="flex items-center space-x-2">
          <div
            className="animate-spin rounded-full h-6 w-6 border-b-2"
            style={{ borderColor: colors.primary }}
          />
          <span style={{ color: colors.textSecondary }}>Loading...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center p-8" data-testid={testId}>
        <div className="text-center">
          <div className="text-red-500 mb-2">⚠️</div>
          <div style={{ color: colors.textSecondary }}>{error}</div>
        </div>
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center p-8" data-testid={testId}>
        <div style={{ color: colors.textSecondary }}>{emptyMessage}</div>
      </div>
    );
  }

  return (
    <div className={cn('w-full', className)} data-testid={testId}>
      <Table hoverable={hoverable}>
        {showHeader && (
          <thead>
            <TableRow>
              {selectable && (
                <TableHeader width="50px">
                  <input
                    type="checkbox"
                    className="rounded"
                    // TODO: Implement select all functionality
                  />
                </TableHeader>
              )}
              {columns.map(column => (
                <TableHeader
                  key={column.key}
                  sortable={column.sortable}
                  sortDirection={
                    sortColumn === column.key ? sortDirection : null
                  }
                  onSort={() => handleSort(column)}
                  align={column.align}
                  width={column.width}
                  className={column.className}
                >
                  {column.label}
                </TableHeader>
              ))}
              {renderRowActions && (
                <TableHeader align="right" width="100px">
                  Actions
                </TableHeader>
              )}
            </TableRow>
          </thead>
        )}
        <tbody>
          {data.map((item, index) => {
            const key = getRowKey(item, index);
            const selected = isSelected(item);

            return (
              <TableRow
                key={key}
                selected={selected}
                hoverable={hoverable}
                clickable={!!onRowClick}
                striped={striped}
                index={index}
                onClick={() => onRowClick?.(item, index)}
              >
                {selectable && (
                  <TableCell>
                    <input
                      type="checkbox"
                      className="rounded"
                      checked={selected}
                      onChange={e => handleRowSelect(item, e)}
                      onClick={e => e.stopPropagation()}
                    />
                  </TableCell>
                )}
                {columns.map(column => (
                  <TableCell
                    key={column.key}
                    align={column.align}
                    width={column.width}
                    className={column.className}
                  >
                    {renderCell(column, item, index)}
                  </TableCell>
                ))}
                {renderRowActions && (
                  <TableCell align="right">
                    <div onClick={e => e.stopPropagation()}>
                      {renderRowActions(item, index)}
                    </div>
                  </TableCell>
                )}
              </TableRow>
            );
          })}
        </tbody>
      </Table>
    </div>
  );
};
