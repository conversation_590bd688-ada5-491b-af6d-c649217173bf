import React from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../utils/cn';

export interface ChipProps {
  children: React.ReactNode;
  variant?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
  size?: 'xs' | 'sm' | 'md' | 'lg';
  outline?: boolean;
  deletable?: boolean;
  disabled?: boolean;
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
  onDelete?: () => void;
  onClick?: (event: React.MouseEvent<HTMLDivElement>) => void;
  className?: string;
  'data-testid'?: string;
}

export const Chip: React.FC<ChipProps> = ({
  children,
  variant = 'default',
  size = 'md',
  outline = false,
  deletable = false,
  disabled = false,
  startIcon,
  endIcon,
  onDelete,
  onClick,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const sizeClasses = {
    xs: 'px-2 py-0.5 text-xs h-5',
    sm: 'px-2.5 py-1 text-xs h-6',
    md: 'px-3 py-1.5 text-sm h-8',
    lg: 'px-4 py-2 text-sm h-10',
  };

  const iconSizes = {
    xs: 'w-3 h-3',
    sm: 'w-3.5 h-3.5',
    md: 'w-4 h-4',
    lg: 'w-5 h-5',
  };

  const variantStyles = {
    default: {
      backgroundColor: outline ? 'transparent' : colors.muted,
      color: colors.mutedForeground,
      borderColor: colors.border,
    },
    primary: {
      backgroundColor: outline ? 'transparent' : colors.primary,
      color: outline ? colors.primary : colors.primaryForeground,
      borderColor: colors.primary,
    },
    secondary: {
      backgroundColor: outline ? 'transparent' : colors.secondary,
      color: outline ? colors.secondary : colors.secondaryForeground,
      borderColor: colors.secondary,
    },
    success: {
      backgroundColor: outline ? 'transparent' : '#10b981',
      color: outline ? '#10b981' : '#ffffff',
      borderColor: '#10b981',
    },
    warning: {
      backgroundColor: outline ? 'transparent' : '#f59e0b',
      color: outline ? '#f59e0b' : '#ffffff',
      borderColor: '#f59e0b',
    },
    error: {
      backgroundColor: outline ? 'transparent' : '#ef4444',
      color: outline ? '#ef4444' : '#ffffff',
      borderColor: '#ef4444',
    },
    info: {
      backgroundColor: outline ? 'transparent' : '#3b82f6',
      color: outline ? '#3b82f6' : '#ffffff',
      borderColor: '#3b82f6',
    },
  };

  const currentStyle = variantStyles[variant];

  const handleDelete = (event: React.MouseEvent) => {
    event.stopPropagation();
    onDelete?.();
  };

  return (
    <div
      className={cn(
        'inline-flex items-center justify-center font-medium rounded-full',
        'border transition-all duration-200',
        sizeClasses[size],
        onClick && !disabled && 'cursor-pointer hover:opacity-80',
        disabled && 'opacity-50 cursor-not-allowed',
        className
      )}
      style={{
        backgroundColor: currentStyle.backgroundColor,
        color: currentStyle.color,
        borderColor: outline ? currentStyle.borderColor : 'transparent',
      }}
      onClick={!disabled ? onClick : undefined}
      data-testid={testId}
    >
      {startIcon && (
        <div className={cn('mr-1.5', iconSizes[size])}>
          {startIcon}
        </div>
      )}
      
      <span className="truncate">{children}</span>
      
      {endIcon && !deletable && (
        <div className={cn('ml-1.5', iconSizes[size])}>
          {endIcon}
        </div>
      )}
      
      {deletable && (
        <button
          className={cn(
            'ml-1.5 rounded-full hover:bg-black hover:bg-opacity-20 transition-colors',
            iconSizes[size],
            'flex items-center justify-center'
          )}
          onClick={handleDelete}
          disabled={disabled}
          aria-label="Remove"
        >
          <span className="text-current">×</span>
        </button>
      )}
    </div>
  );
};
