import React from 'react';
import { cn } from '../../../../utils/cn';
import type { ResponsiveLayoutProps, ResponsiveBreakpoint } from '../../../types/layout';

export interface ResponsiveContainerProps extends ResponsiveLayoutProps {
  children: React.ReactNode;
  maxWidth?: {
    xs?: string;
    sm?: string;
    md?: string;
    lg?: string;
    xl?: string;
    '2xl'?: string;
  };
  padding?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    '2xl'?: number;
  };
}

/**
 * ResponsiveContainer component for responsive layouts
 * Provides different styling based on screen size breakpoints
 */
export const ResponsiveContainer: React.FC<ResponsiveContainerProps> = ({
  children,
  maxWidth,
  padding,
  breakpoint = 'md',
  responsive = true,
  className = '',
  'data-testid': testId,
}) => {
  const getResponsiveClasses = () => {
    if (!responsive) return '';

    const classes: string[] = [];

    // Max width classes
    if (maxWidth) {
      Object.entries(maxWidth).forEach(([bp, width]) => {
        if (bp === 'xs') {
          classes.push(`max-w-[${width}]`);
        } else {
          classes.push(`${bp}:max-w-[${width}]`);
        }
      });
    }

    // Padding classes
    if (padding) {
      Object.entries(padding).forEach(([bp, pad]) => {
        const paddingClass = `p-${pad}`;
        if (bp === 'xs') {
          classes.push(paddingClass);
        } else {
          classes.push(`${bp}:${paddingClass}`);
        }
      });
    }

    return classes.join(' ');
  };

  const containerClasses = cn(
    'w-full mx-auto',
    responsive && getResponsiveClasses(),
    className
  );

  return (
    <div className={containerClasses} data-testid={testId}>
      {children}
    </div>
  );
};
