import React, { useEffect, useState } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../utils/cn';

export interface SnackbarProps {
  message: string;
  type?: 'info' | 'success' | 'warning' | 'error';
  duration?: number; // in milliseconds, 0 means no auto-dismiss
  position?: 'top' | 'bottom';
  action?: {
    label: string;
    onClick: () => void;
  };
  onClose?: () => void;
  open?: boolean;
  className?: string;
  'data-testid'?: string;
}

/**
 * Snackbar component for brief messages at the bottom or top of the screen
 * Similar to Material Design snackbars
 */
export const Snackbar: React.FC<SnackbarProps> = ({
  message,
  type = 'info',
  duration = 4000,
  position = 'bottom',
  action,
  onClose,
  open = true,
  className = '',
  'data-testid': testId,
}) => {
  const [isVisible, setIsVisible] = useState(open);
  const [isAnimating, setIsAnimating] = useState(false);
  const { colors } = useThemeStore();

  useEffect(() => {
    setIsVisible(open);
    if (open) {
      setIsAnimating(true);
    }
  }, [open]);

  useEffect(() => {
    if (isVisible && duration > 0) {
      const timer = setTimeout(() => {
        handleClose();
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [isVisible, duration]);

  const handleClose = () => {
    setIsAnimating(false);
    setTimeout(() => {
      setIsVisible(false);
      onClose?.();
    }, 200);
  };

  const handleActionClick = () => {
    action?.onClick();
    handleClose();
  };

  const typeStyles = {
    info: {
      backgroundColor: colors.primary,
      color: '#ffffff',
    },
    success: {
      backgroundColor: '#10b981',
      color: '#ffffff',
    },
    warning: {
      backgroundColor: '#f59e0b',
      color: '#ffffff',
    },
    error: {
      backgroundColor: '#ef4444',
      color: '#ffffff',
    },
  };

  const positionClasses = {
    top: 'top-4',
    bottom: 'bottom-4',
  };

  const animationClasses = isAnimating
    ? position === 'bottom'
      ? 'translate-y-0 opacity-100'
      : 'translate-y-0 opacity-100'
    : position === 'bottom'
    ? 'translate-y-full opacity-0'
    : '-translate-y-full opacity-0';

  if (!isVisible) return null;

  return (
    <div
      className={cn(
        'fixed left-1/2 transform -translate-x-1/2 z-50 min-w-[288px] max-w-[568px]',
        'px-4 py-3 rounded-lg shadow-lg transition-all duration-200 ease-in-out',
        'flex items-center justify-between',
        positionClasses[position],
        animationClasses,
        className
      )}
      style={typeStyles[type]}
      data-testid={testId}
    >
      <span className="flex-1 text-sm font-medium">{message}</span>
      
      <div className="flex items-center gap-2 ml-4">
        {action && (
          <button
            onClick={handleActionClick}
            className="text-sm font-medium uppercase tracking-wide hover:opacity-80 transition-opacity"
            style={{ color: 'inherit' }}
          >
            {action.label}
          </button>
        )}
        
        <button
          onClick={handleClose}
          className="p-1 hover:opacity-80 transition-opacity"
          aria-label="Close"
        >
          <svg
            className="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>
    </div>
  );
};
