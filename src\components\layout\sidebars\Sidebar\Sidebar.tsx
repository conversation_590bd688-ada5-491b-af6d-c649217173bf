import React from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../utils/cn';
import type { SidebarProps } from '../../../types/layout';

/**
 * Sidebar component for side navigation and panels
 * Provides collapsible sidebar with responsive behavior
 */
export const Sidebar: React.FC<SidebarProps & { children: React.ReactNode }> = ({
  children,
  variant = 'default',
  position = 'left',
  collapsible = false,
  collapsed = false,
  width = 'md',
  overlay = false,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const widthClasses = {
    xs: 'w-48',
    sm: 'w-56',
    md: 'w-64',
    lg: 'w-72',
    xl: 'w-80',
    '2xl': 'w-96',
  };

  const sidebarClasses = cn(
    'flex flex-col transition-all duration-300 ease-in-out',
    variant === 'compact' && 'border-r',
    variant === 'overlay' && 'absolute z-50 shadow-lg',
    position === 'right' && 'border-l',
    position === 'left' && 'border-r',
    typeof width === 'string' && widthClasses[width as keyof typeof widthClasses],
    collapsed && collapsible && 'w-16',
    overlay && 'fixed top-0 h-full',
    overlay && position === 'left' && 'left-0',
    overlay && position === 'right' && 'right-0',
    className
  );

  const sidebarStyle = {
    backgroundColor: colors.surface,
    borderColor: colors.border,
    ...(typeof width === 'string' && !collapsed ? {} : { width }),
  };

  return (
    <aside
      className={sidebarClasses}
      style={sidebarStyle}
      data-testid={testId}
    >
      {children}
    </aside>
  );
};
