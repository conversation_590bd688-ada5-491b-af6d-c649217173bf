import React, { useState } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../utils/cn';

export interface TabItem {
  id: string;
  label: string;
  content: React.ReactNode;
  disabled?: boolean;
  icon?: React.ReactNode;
  badge?: string | number;
}

export interface TabsBasicProps {
  items: TabItem[];
  defaultActiveTab?: string;
  activeTab?: string;
  onTabChange?: (tabId: string) => void;
  variant?: 'default' | 'pills' | 'underline';
  size?: 'sm' | 'md' | 'lg';
  orientation?: 'horizontal' | 'vertical';
  className?: string;
  tabsClassName?: string;
  contentClassName?: string;
  'data-testid'?: string;
}

export const TabsBasic: React.FC<TabsBasicProps> = ({
  items,
  defaultActiveTab,
  activeTab: controlledActiveTab,
  onTabChange,
  variant = 'default',
  size = 'md',
  orientation = 'horizontal',
  className = '',
  tabsClassName = '',
  contentClassName = '',
  'data-testid': testId,
}) => {
  const [internalActiveTab, setInternalActiveTab] = useState(
    defaultActiveTab || items[0]?.id || ''
  );
  
  const { colors } = useThemeStore();
  
  const activeTab = controlledActiveTab ?? internalActiveTab;
  const activeItem = items.find(item => item.id === activeTab);

  const handleTabClick = (tabId: string) => {
    if (!controlledActiveTab) {
      setInternalActiveTab(tabId);
    }
    onTabChange?.(tabId);
  };

  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg',
  };

  const getTabStyles = (item: TabItem, isActive: boolean) => {
    const baseStyles = {
      color: isActive ? colors.primary : colors.mutedForeground,
      backgroundColor: 'transparent',
      borderColor: 'transparent',
    };

    if (variant === 'pills') {
      return {
        ...baseStyles,
        backgroundColor: isActive ? colors.primary + '15' : 'transparent',
        borderColor: isActive ? colors.primary + '30' : 'transparent',
      };
    }

    if (variant === 'underline') {
      return {
        ...baseStyles,
        borderBottomColor: isActive ? colors.primary : 'transparent',
      };
    }

    return {
      ...baseStyles,
      backgroundColor: isActive ? colors.background : colors.muted + '50',
      borderColor: isActive ? colors.border : 'transparent',
    };
  };

  const tabListClasses = cn(
    'flex',
    orientation === 'horizontal' ? 'flex-row border-b' : 'flex-col border-r',
    variant === 'pills' && 'gap-1 border-none',
    variant === 'underline' && 'border-b',
    tabsClassName
  );

  const tabClasses = cn(
    'flex items-center gap-2 font-medium transition-all duration-150 cursor-pointer',
    'border focus:outline-none focus:ring-2 focus:ring-offset-2',
    sizeClasses[size],
    variant === 'pills' && 'rounded-md',
    variant === 'underline' && 'border-transparent border-b-2 rounded-none',
    variant === 'default' && 'rounded-t-md border-b-0'
  );

  return (
    <div
      className={cn(
        'w-full',
        orientation === 'horizontal' ? 'flex flex-col' : 'flex flex-row',
        className
      )}
      data-testid={testId}
    >
      {/* Tab List */}
      <div
        className={tabListClasses}
        style={{ borderColor: colors.border }}
        role="tablist"
      >
        {items.map((item) => {
          const isActive = item.id === activeTab;
          const tabStyles = getTabStyles(item, isActive);
          
          return (
            <button
              key={item.id}
              className={cn(
                tabClasses,
                item.disabled && 'opacity-50 cursor-not-allowed'
              )}
              style={tabStyles}
              onClick={() => !item.disabled && handleTabClick(item.id)}
              disabled={item.disabled}
              role="tab"
              aria-selected={isActive}
              aria-controls={`tabpanel-${item.id}`}
              id={`tab-${item.id}`}
            >
              {item.icon && <span>{item.icon}</span>}
              <span>{item.label}</span>
              {item.badge && (
                <span
                  className="px-1.5 py-0.5 text-xs rounded-full"
                  style={{
                    backgroundColor: colors.primary,
                    color: colors.primaryForeground,
                  }}
                >
                  {item.badge}
                </span>
              )}
            </button>
          );
        })}
      </div>

      {/* Tab Content */}
      <div
        className={cn(
          'flex-1 p-4',
          orientation === 'vertical' && 'border-l-0',
          contentClassName
        )}
        role="tabpanel"
        aria-labelledby={`tab-${activeTab}`}
        id={`tabpanel-${activeTab}`}
      >
        {activeItem?.content}
      </div>
    </div>
  );
};
