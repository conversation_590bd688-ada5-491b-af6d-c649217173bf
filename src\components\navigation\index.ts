// Navigation Components - Components for navigation and routing
// These components handle user navigation throughout the application

// Menus - Navigation menus, context menus, and breadcrumbs
export * from './menus';

// Pagination - Page navigation and pagination controls
export * from './pagination';

// Tabs - Tabbed navigation and view switchers
export * from './tabs';

// Routing - Navigation links and routing utilities
export * from './routing';

// ✅ Implemented Components:
//
// Menus:
// - Breadcrumb: Navigation breadcrumbs with truncation and home icon
// - BreadcrumbGlobal: Enhanced breadcrumb from global components
// - Menu: Dropdown menu with nested submenus and positioning
// - ContextMenu: Right-click context menu with positioning
// - NavigationMenu: Main navigation menu with variants and badges
//
// Pagination:
// - Pagination: Page navigation with customizable controls
// - Stepper: Step-by-step navigation with status indicators
// - PageSizeSelector: Items per page selector with total count
// - PageInfo: Current page information display
//
// Tabs:
// - ViewModeSwitcher: View mode switching (deprecated, use ViewModeSelector)
// - ViewToggle: Simple view toggle (deprecated, use ViewModeSelector)
// - Tabs: Layout tabs component
// - TabsBasic: Basic tabs with tab items
// - ViewModeSelector: Enhanced view mode selector from global
//
// Routing:
// - Link: Enhanced link component with variants and external support
// - NavLink: Navigation link with active states and badges

// TODO: Future navigation components to implement
// Main Navigation
// - NavBar: Main navigation bar component
// - SideNav: Side navigation panel
//
// Advanced Navigation
// - Wizard: Multi-step form navigation
// - CommandPalette: Quick navigation and search
//
// Routing
// - RouterGuard: Route protection and authentication
// - RouteTransition: Animated route transitions
