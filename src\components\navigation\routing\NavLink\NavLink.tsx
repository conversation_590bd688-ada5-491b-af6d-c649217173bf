import React from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../utils/cn';

export interface NavLinkProps {
  children: React.ReactNode;
  href?: string;
  to?: string; // For React Router compatibility
  active?: boolean;
  variant?: 'default' | 'pills' | 'underline' | 'sidebar';
  size?: 'sm' | 'md' | 'lg';
  icon?: React.ReactNode;
  badge?: string | number;
  disabled?: boolean;
  onClick?: (event: React.MouseEvent<HTMLAnchorElement>) => void;
  className?: string;
  'data-testid'?: string;
}

export const NavLink: React.FC<NavLinkProps> = ({
  children,
  href,
  to,
  active = false,
  variant = 'default',
  size = 'md',
  icon,
  badge,
  disabled = false,
  onClick,
  className = '',
  'data-testid': testId,
  ...props
}) => {
  const { colors } = useThemeStore();

  const sizeClasses = {
    sm: 'px-2 py-1 text-sm',
    md: 'px-3 py-2 text-base',
    lg: 'px-4 py-3 text-lg',
  };

  const getVariantClasses = () => {
    switch (variant) {
      case 'pills':
        return cn(
          'rounded-full transition-all duration-200',
          active && 'font-medium',
          !disabled && 'hover:bg-opacity-50'
        );
      case 'underline':
        return cn(
          'border-b-2 border-transparent transition-all duration-200',
          active && 'border-current font-medium',
          !disabled && 'hover:border-current hover:border-opacity-50'
        );
      case 'sidebar':
        return cn(
          'rounded-lg transition-all duration-200 w-full justify-start',
          active && 'font-medium',
          !disabled && 'hover:bg-opacity-50'
        );
      default:
        return cn(
          'rounded-md transition-all duration-200',
          active && 'font-medium',
          !disabled && 'hover:bg-opacity-50'
        );
    }
  };

  const getVariantStyle = () => {
    if (disabled) {
      return {
        color: colors.mutedForeground,
        backgroundColor: 'transparent',
      };
    }

    if (active) {
      return {
        color: colors.primary,
        backgroundColor: variant === 'pills' || variant === 'sidebar' 
          ? colors.primary + '15' 
          : 'transparent',
      };
    }

    return {
      color: colors.text,
      backgroundColor: 'transparent',
    };
  };

  const handleClick = (event: React.MouseEvent<HTMLAnchorElement>) => {
    if (disabled) {
      event.preventDefault();
      return;
    }

    onClick?.(event);
  };

  const renderBadge = () => {
    if (!badge) return null;

    return (
      <span
        className="ml-2 px-1.5 py-0.5 text-xs font-medium rounded-full"
        style={{
          backgroundColor: colors.primary,
          color: colors.primaryForeground,
        }}
      >
        {badge}
      </span>
    );
  };

  const linkUrl = href || to;

  return (
    <a
      {...props}
      href={linkUrl}
      onClick={handleClick}
      className={cn(
        'flex items-center no-underline',
        sizeClasses[size],
        getVariantClasses(),
        disabled && 'opacity-50 cursor-not-allowed pointer-events-none',
        className
      )}
      style={getVariantStyle()}
      onMouseEnter={e => {
        if (!disabled && !active) {
          e.currentTarget.style.backgroundColor = colors.hover + '15';
        }
      }}
      onMouseLeave={e => {
        if (!disabled && !active) {
          e.currentTarget.style.backgroundColor = 'transparent';
        }
      }}
      data-testid={testId}
    >
      {icon && (
        <span className="mr-2 flex-shrink-0">{icon}</span>
      )}
      
      <span className="flex-1">{children}</span>
      
      {renderBadge()}
    </a>
  );
};
