import React from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../utils/cn';

export interface BadgeProps {
  children: React.ReactNode;
  variant?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
  size?: 'xs' | 'sm' | 'md' | 'lg';
  shape?: 'rounded' | 'pill' | 'square';
  outline?: boolean;
  dot?: boolean;
  className?: string;
  'data-testid'?: string;
}

export const Badge: React.FC<BadgeProps> = ({
  children,
  variant = 'default',
  size = 'sm',
  shape = 'rounded',
  outline = false,
  dot = false,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const sizeClasses = {
    xs: 'px-1.5 py-0.5 text-xs',
    sm: 'px-2 py-1 text-xs',
    md: 'px-2.5 py-1 text-sm',
    lg: 'px-3 py-1.5 text-sm',
  };

  const shapeClasses = {
    rounded: 'rounded',
    pill: 'rounded-full',
    square: 'rounded-none',
  };

  const variantStyles = {
    default: {
      backgroundColor: outline ? 'transparent' : colors.muted,
      color: colors.mutedForeground,
      borderColor: colors.border,
    },
    primary: {
      backgroundColor: outline ? 'transparent' : colors.primary,
      color: outline ? colors.primary : colors.primaryForeground,
      borderColor: colors.primary,
    },
    secondary: {
      backgroundColor: outline ? 'transparent' : colors.secondary,
      color: outline ? colors.secondary : colors.secondaryForeground,
      borderColor: colors.secondary,
    },
    success: {
      backgroundColor: outline ? 'transparent' : '#10b981',
      color: outline ? '#10b981' : '#ffffff',
      borderColor: '#10b981',
    },
    warning: {
      backgroundColor: outline ? 'transparent' : '#f59e0b',
      color: outline ? '#f59e0b' : '#ffffff',
      borderColor: '#f59e0b',
    },
    error: {
      backgroundColor: outline ? 'transparent' : '#ef4444',
      color: outline ? '#ef4444' : '#ffffff',
      borderColor: '#ef4444',
    },
    info: {
      backgroundColor: outline ? 'transparent' : '#3b82f6',
      color: outline ? '#3b82f6' : '#ffffff',
      borderColor: '#3b82f6',
    },
  };

  const currentStyle = variantStyles[variant];

  if (dot) {
    return (
      <span
        className={cn(
          'inline-block w-2 h-2 rounded-full',
          className
        )}
        style={{
          backgroundColor: currentStyle.borderColor,
        }}
        data-testid={testId}
      />
    );
  }

  return (
    <span
      className={cn(
        'inline-flex items-center justify-center font-medium transition-colors',
        'border',
        sizeClasses[size],
        shapeClasses[shape],
        className
      )}
      style={{
        backgroundColor: currentStyle.backgroundColor,
        color: currentStyle.color,
        borderColor: outline ? currentStyle.borderColor : 'transparent',
      }}
      data-testid={testId}
    >
      {children}
    </span>
  );
};
