// Form Components - Components for building forms and handling user input
// These components provide form functionality with validation and accessibility

// Core form components (migrated from ui/)
export { Button } from './Button';
export type { ButtonProps } from './Button';

export { Input } from './Input';
export type { InputProps } from './Input';

export { Label } from './Label';
export type { LabelProps } from './Label';

// Re-export from inputs for backward compatibility
export { TextArea } from '../inputs/basic/TextArea';
export type { TextAreaProps } from '../inputs/basic/TextArea';

// Specialized form components (re-export from inputs)
export { CountrySelector } from '../inputs/selectors/CountrySelector';
export type { CountrySelectorProps, Country } from '../inputs/selectors/CountrySelector';

// Form types
export type {
  FormSize,
  FormVariant,
  FormValidationState,
  BaseFormProps,
  FormFieldProps,
  FormControlProps,
  FormValidationRule,
  FormFieldState,
  FormState,
  FormConfig,
  SelectOption,
  SelectGroup,
  FileUploadConfig,
  UploadedFile,
} from './types';
