import React from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../utils/cn';

export interface StatusIndicatorProps {
  status: 'online' | 'offline' | 'away' | 'busy' | 'idle' | 'success' | 'warning' | 'error' | 'info';
  size?: 'xs' | 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  label?: string;
  pulse?: boolean;
  className?: string;
  'data-testid'?: string;
}

export const StatusIndicator: React.FC<StatusIndicatorProps> = ({
  status,
  size = 'md',
  showLabel = false,
  label,
  pulse = false,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const sizeClasses = {
    xs: 'w-2 h-2',
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-5 h-5',
  };

  const statusColors = {
    online: '#10b981',
    offline: '#6b7280',
    away: '#f59e0b',
    busy: '#ef4444',
    idle: '#8b5cf6',
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
    info: '#3b82f6',
  };

  const statusLabels = {
    online: 'Online',
    offline: 'Offline',
    away: 'Away',
    busy: 'Busy',
    idle: 'Idle',
    success: 'Success',
    warning: 'Warning',
    error: 'Error',
    info: 'Info',
  };

  const displayLabel = label || statusLabels[status];

  return (
    <div
      className={cn(
        'inline-flex items-center',
        showLabel && 'space-x-2',
        className
      )}
      data-testid={testId}
    >
      <div
        className={cn(
          'rounded-full',
          sizeClasses[size],
          pulse && 'animate-pulse'
        )}
        style={{
          backgroundColor: statusColors[status],
        }}
      />
      
      {showLabel && (
        <span
          className="text-sm font-medium"
          style={{ color: colors.text }}
        >
          {displayLabel}
        </span>
      )}
    </div>
  );
};
