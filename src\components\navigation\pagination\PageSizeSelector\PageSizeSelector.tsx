import React from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../utils/cn';

export interface PageSizeSelectorProps {
  pageSize: number;
  pageSizeOptions?: number[];
  onPageSizeChange: (pageSize: number) => void;
  totalItems?: number;
  showLabel?: boolean;
  label?: string;
  disabled?: boolean;
  className?: string;
  'data-testid'?: string;
}

export const PageSizeSelector: React.FC<PageSizeSelectorProps> = ({
  pageSize,
  pageSizeOptions = [10, 25, 50, 100],
  onPageSizeChange,
  totalItems,
  showLabel = true,
  label = 'Items per page:',
  disabled = false,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const handleChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const newPageSize = parseInt(event.target.value, 10);
    onPageSizeChange(newPageSize);
  };

  return (
    <div
      className={cn('flex items-center space-x-2', className)}
      data-testid={testId}
    >
      {showLabel && (
        <label
          className="text-sm font-medium"
          style={{ color: colors.text }}
        >
          {label}
        </label>
      )}
      
      <select
        value={pageSize}
        onChange={handleChange}
        disabled={disabled}
        className={cn(
          'px-3 py-1 text-sm border rounded-md transition-colors',
          'focus:outline-none focus:ring-2 focus:ring-opacity-50',
          disabled && 'opacity-50 cursor-not-allowed'
        )}
        style={{
          backgroundColor: colors.surface,
          borderColor: colors.border,
          color: colors.text,
          focusRingColor: colors.primary,
        }}
      >
        {pageSizeOptions.map(option => (
          <option key={option} value={option}>
            {option}
          </option>
        ))}
      </select>

      {totalItems !== undefined && (
        <span
          className="text-sm"
          style={{ color: colors.textSecondary }}
        >
          of {totalItems} items
        </span>
      )}
    </div>
  );
};
