// Data Display Components - Components for displaying and organizing data
// These components handle the presentation of structured data

// Cards and Tiles - Content containers and display cards
export * from './cards';

// Typography - Text display and content formatting
export * from './typography';

// Media - Avatars, images, and media display
export * from './media';

// Indicators - Badges, chips, and status indicators
export * from './indicators';

// Tables - Data tables and list displays
export * from './tables';

// Timeline - Timeline and activity components
export * from './timeline';

// ✅ Implemented Components:
//
// Cards:
// - Card: Basic card component (deprecated, use GlobalCard)
// - AppTile: Application tile component
// - InfoCard: Information display card with icon and value
// - StatsCard: Statistical data display card with change indicators
// - GlobalCard: Enhanced card component from global
//
// Typography:
// - Text: Basic text component (deprecated, use GlobalText)
// - Caption: Caption text component
// - Heading: Heading component
// - GlobalText: Enhanced text component from global
// - GlobalHeading: Enhanced heading component from global
//
// Media:
// - Avatar: User avatar component with status indicators
// - UserAvatarDropdown: Avatar with dropdown functionality
// - AvatarGroup: Group of avatars with overflow handling
//
// Indicators:
// - Badge: Status badges with variants and sizes
// - Chip: Interactive chips with delete functionality
// - StatusIndicator: Status dots with labels and pulse animation
//
// Tables:
// - Table: Basic table component with variants
// - TableHeader: Sortable table header component
// - TableRow: Table row with selection and hover states
// - TableCell: Table cell with alignment and truncation
// - List: Flexible list component with variants
// - ListItem: List item with icons and actions
// - DataTable: Feature-rich data table with sorting, filtering, pagination
//
// Timeline:
// - Timeline: Timeline container with orientation support
// - TimelineItem: Individual timeline entries with icons and timestamps
// - ActivityFeed: Activity stream with user avatars and grouping

// TODO: Future data display components to implement
// Advanced Lists
// - VirtualList: Performance-optimized virtual scrolling list
// - DataList: Enhanced list with sorting and filtering
//
// Specialized Cards
// - MediaCard: Media content card
// - ActionCard: Interactive card with actions
//
// Search and Filter
// - SearchResults: Search results display
// - FilterPanel: Advanced filtering interface
// - DataGrid: Excel-like data grid component
