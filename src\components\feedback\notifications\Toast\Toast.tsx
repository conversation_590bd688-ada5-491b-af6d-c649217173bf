import React, { useEffect, useState } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../utils/cn';

export interface ToastProps {
  id?: string;
  title?: string;
  message: string;
  type?: 'info' | 'success' | 'warning' | 'error';
  duration?: number; // in milliseconds, 0 means no auto-dismiss
  dismissible?: boolean;
  onDismiss?: (id?: string) => void;
  action?: {
    label: string;
    onClick: () => void;
  };
  className?: string;
  'data-testid'?: string;
}

export const Toast: React.FC<ToastProps> = ({
  id,
  title,
  message,
  type = 'info',
  duration = 5000,
  dismissible = true,
  onDismiss,
  action,
  className = '',
  'data-testid': testId,
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const [isExiting, setIsExiting] = useState(false);
  const { colors } = useThemeStore();

  // Auto-dismiss after duration
  useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(() => {
        handleDismiss();
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [duration]);

  const handleDismiss = () => {
    setIsExiting(true);
    setTimeout(() => {
      setIsVisible(false);
      onDismiss?.(id);
    }, 200); // Animation duration
  };

  const handleActionClick = () => {
    action?.onClick();
    handleDismiss();
  };

  if (!isVisible) return null;

  const typeStyles = {
    info: {
      backgroundColor: colors.primary + '15',
      borderColor: colors.primary,
      iconColor: colors.primary,
    },
    success: {
      backgroundColor: '#10b981' + '15',
      borderColor: '#10b981',
      iconColor: '#10b981',
    },
    warning: {
      backgroundColor: '#f59e0b' + '15',
      borderColor: '#f59e0b',
      iconColor: '#f59e0b',
    },
    error: {
      backgroundColor: '#ef4444' + '15',
      borderColor: '#ef4444',
      iconColor: '#ef4444',
    },
  };

  const typeIcons = {
    info: '🛈',
    success: '✓',
    warning: '⚠',
    error: '✕',
  };

  const currentStyle = typeStyles[type];

  return (
    <div
      className={cn(
        'relative flex items-start gap-3 p-4 rounded-lg border shadow-lg transition-all duration-200',
        'transform translate-x-0 opacity-100',
        isExiting && 'translate-x-full opacity-0',
        className
      )}
      style={{
        backgroundColor: currentStyle.backgroundColor,
        borderColor: currentStyle.borderColor,
        color: colors.text,
      }}
      data-testid={testId}
    >
      {/* Icon */}
      <div
        className="flex-shrink-0 w-5 h-5 flex items-center justify-center text-sm font-bold"
        style={{ color: currentStyle.iconColor }}
      >
        {typeIcons[type]}
      </div>

      {/* Content */}
      <div className="flex-1 min-w-0">
        {title && (
          <div className="font-medium text-sm mb-1" style={{ color: colors.text }}>
            {title}
          </div>
        )}
        <div className="text-sm" style={{ color: colors.mutedForeground }}>
          {message}
        </div>
      </div>

      {/* Action Button */}
      {action && (
        <button
          onClick={handleActionClick}
          className="flex-shrink-0 text-sm font-medium px-3 py-1 rounded transition-colors"
          style={{
            color: currentStyle.iconColor,
            backgroundColor: 'transparent',
          }}
        >
          {action.label}
        </button>
      )}

      {/* Dismiss Button */}
      {dismissible && (
        <button
          onClick={handleDismiss}
          className="flex-shrink-0 w-5 h-5 flex items-center justify-center text-sm opacity-60 hover:opacity-100 transition-opacity"
          style={{ color: colors.mutedForeground }}
          aria-label="Dismiss"
        >
          ✕
        </button>
      )}
    </div>
  );
};
