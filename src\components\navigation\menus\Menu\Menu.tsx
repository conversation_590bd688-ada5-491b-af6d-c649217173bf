import React, { useState, useRef, useEffect } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../utils/cn';

export interface MenuItem {
  id: string;
  label: string;
  icon?: React.ReactNode;
  href?: string;
  onClick?: () => void;
  disabled?: boolean;
  divider?: boolean;
  children?: MenuItem[];
}

export interface MenuProps {
  items: MenuItem[];
  trigger: React.ReactNode;
  placement?: 'bottom-start' | 'bottom-end' | 'top-start' | 'top-end' | 'right-start' | 'left-start';
  offset?: number;
  closeOnClick?: boolean;
  disabled?: boolean;
  className?: string;
  'data-testid'?: string;
}

export const Menu: React.FC<MenuProps> = ({
  items,
  trigger,
  placement = 'bottom-start',
  offset = 4,
  closeOnClick = true,
  disabled = false,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const [isOpen, setIsOpen] = useState(false);
  const [openSubmenus, setOpenSubmenus] = useState<Set<string>>(new Set());
  const menuRef = useRef<HTMLDivElement>(null);
  const triggerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        menuRef.current &&
        triggerRef.current &&
        !menuRef.current.contains(event.target as Node) &&
        !triggerRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
        setOpenSubmenus(new Set());
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const handleTriggerClick = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
    }
  };

  const handleItemClick = (item: MenuItem) => {
    if (item.disabled) return;

    if (item.children && item.children.length > 0) {
      setOpenSubmenus(prev => {
        const newSet = new Set(prev);
        if (newSet.has(item.id)) {
          newSet.delete(item.id);
        } else {
          newSet.add(item.id);
        }
        return newSet;
      });
    } else {
      if (item.onClick) {
        item.onClick();
      }
      if (closeOnClick) {
        setIsOpen(false);
        setOpenSubmenus(new Set());
      }
    }
  };

  const getMenuPosition = () => {
    const baseClasses = 'absolute z-50';
    
    switch (placement) {
      case 'bottom-start':
        return `${baseClasses} top-full left-0 mt-${offset}`;
      case 'bottom-end':
        return `${baseClasses} top-full right-0 mt-${offset}`;
      case 'top-start':
        return `${baseClasses} bottom-full left-0 mb-${offset}`;
      case 'top-end':
        return `${baseClasses} bottom-full right-0 mb-${offset}`;
      case 'right-start':
        return `${baseClasses} top-0 left-full ml-${offset}`;
      case 'left-start':
        return `${baseClasses} top-0 right-full mr-${offset}`;
      default:
        return `${baseClasses} top-full left-0 mt-${offset}`;
    }
  };

  const renderMenuItem = (item: MenuItem, level = 0) => {
    if (item.divider) {
      return (
        <div
          key={item.id}
          className="my-1 border-t"
          style={{ borderColor: colors.border }}
        />
      );
    }

    const hasChildren = item.children && item.children.length > 0;
    const isSubmenuOpen = openSubmenus.has(item.id);

    return (
      <div key={item.id} className="relative">
        <button
          className={cn(
            'w-full flex items-center justify-between px-3 py-2 text-sm text-left',
            'hover:bg-opacity-50 transition-colors rounded-md',
            item.disabled && 'opacity-50 cursor-not-allowed',
            level > 0 && 'pl-6'
          )}
          style={{
            color: item.disabled ? colors.mutedForeground : colors.text,
            backgroundColor: 'transparent',
          }}
          onMouseEnter={e => {
            if (!item.disabled) {
              e.currentTarget.style.backgroundColor = colors.hover + '15';
            }
          }}
          onMouseLeave={e => {
            e.currentTarget.style.backgroundColor = 'transparent';
          }}
          onClick={() => handleItemClick(item)}
          disabled={item.disabled}
        >
          <div className="flex items-center space-x-2">
            {item.icon && (
              <span className="flex-shrink-0">{item.icon}</span>
            )}
            <span>{item.label}</span>
          </div>
          
          {hasChildren && (
            <span className="ml-2">
              {isSubmenuOpen ? '▼' : '▶'}
            </span>
          )}
        </button>

        {hasChildren && isSubmenuOpen && (
          <div className="mt-1 ml-2 border-l pl-2" style={{ borderColor: colors.border }}>
            {item.children!.map(child => renderMenuItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={cn('relative inline-block', className)} data-testid={testId}>
      <div
        ref={triggerRef}
        onClick={handleTriggerClick}
        className={cn(
          'cursor-pointer',
          disabled && 'cursor-not-allowed opacity-50'
        )}
      >
        {trigger}
      </div>

      {isOpen && (
        <div
          ref={menuRef}
          className={cn(
            getMenuPosition(),
            'min-w-48 py-2 rounded-lg border shadow-lg'
          )}
          style={{
            backgroundColor: colors.surface,
            borderColor: colors.border,
          }}
        >
          {items.map(item => renderMenuItem(item))}
        </div>
      )}
    </div>
  );
};
