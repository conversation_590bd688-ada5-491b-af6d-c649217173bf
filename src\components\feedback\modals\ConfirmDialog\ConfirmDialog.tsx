import React from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../utils/cn';

export interface ConfirmDialogProps {
  open?: boolean;
  title?: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  variant?: 'default' | 'danger' | 'warning';
  onConfirm?: () => void;
  onCancel?: () => void;
  onClose?: () => void;
  loading?: boolean;
  className?: string;
  'data-testid'?: string;
}

/**
 * ConfirmDialog component for confirmation dialogs
 * Provides a simple modal for confirming user actions
 */
export const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
  open = false,
  title = 'Confirm Action',
  message,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  variant = 'default',
  onConfirm,
  onCancel,
  onClose,
  loading = false,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const variantStyles = {
    default: {
      confirmButton: {
        backgroundColor: colors.primary,
        color: '#ffffff',
      },
    },
    danger: {
      confirmButton: {
        backgroundColor: '#ef4444',
        color: '#ffffff',
      },
    },
    warning: {
      confirmButton: {
        backgroundColor: '#f59e0b',
        color: '#ffffff',
      },
    },
  };

  const handleConfirm = () => {
    if (!loading) {
      onConfirm?.();
    }
  };

  const handleCancel = () => {
    if (!loading) {
      onCancel?.();
      onClose?.();
    }
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget && !loading) {
      onClose?.();
    }
  };

  if (!open) return null;

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center p-4"
      onClick={handleBackdropClick}
      data-testid={testId}
    >
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black bg-opacity-50 backdrop-blur-sm"
        style={{ backgroundColor: colors.background + '80' }}
      />
      
      {/* Dialog */}
      <div
        className={cn(
          'relative w-full max-w-md rounded-lg shadow-xl',
          'transform transition-all duration-200 ease-out',
          'scale-100 opacity-100',
          className
        )}
        style={{
          backgroundColor: colors.surface,
          borderColor: colors.border,
        }}
      >
        {/* Header */}
        <div className="px-6 py-4 border-b" style={{ borderColor: colors.border }}>
          <h3
            className="text-lg font-semibold"
            style={{ color: colors.text }}
          >
            {title}
          </h3>
        </div>

        {/* Content */}
        <div className="px-6 py-4">
          <p
            className="text-sm leading-relaxed"
            style={{ color: colors.textSecondary }}
          >
            {message}
          </p>
        </div>

        {/* Actions */}
        <div className="flex justify-end gap-3 px-6 py-4 border-t" style={{ borderColor: colors.border }}>
          <button
            onClick={handleCancel}
            disabled={loading}
            className={cn(
              'px-4 py-2 text-sm font-medium rounded-md transition-colors',
              'hover:bg-gray-100 dark:hover:bg-gray-700',
              'disabled:opacity-50 disabled:cursor-not-allowed'
            )}
            style={{
              color: colors.textSecondary,
              borderColor: colors.border,
              borderWidth: '1px',
              borderStyle: 'solid',
            }}
          >
            {cancelText}
          </button>
          
          <button
            onClick={handleConfirm}
            disabled={loading}
            className={cn(
              'px-4 py-2 text-sm font-medium rounded-md transition-colors',
              'hover:opacity-90 disabled:opacity-50 disabled:cursor-not-allowed',
              'flex items-center gap-2'
            )}
            style={variantStyles[variant].confirmButton}
          >
            {loading && (
              <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                />
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                />
              </svg>
            )}
            {confirmText}
          </button>
        </div>
      </div>
    </div>
  );
};
