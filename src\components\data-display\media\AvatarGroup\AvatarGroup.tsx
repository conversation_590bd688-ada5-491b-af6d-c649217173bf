import React from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../utils/cn';
import { Avatar } from '../Avatar';
import type { AvatarProps } from '../Avatar';

export interface AvatarGroupProps {
  avatars: Array<Omit<AvatarProps, 'size' | 'shape'>>;
  size?: AvatarProps['size'];
  shape?: AvatarProps['shape'];
  max?: number;
  spacing?: 'tight' | 'normal' | 'loose';
  showBorder?: boolean;
  onMoreClick?: () => void;
  className?: string;
  'data-testid'?: string;
}

export const AvatarGroup: React.FC<AvatarGroupProps> = ({
  avatars,
  size = 'md',
  shape = 'circle',
  max = 5,
  spacing = 'normal',
  showBorder = true,
  onMoreClick,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const spacingClasses = {
    tight: '-space-x-1',
    normal: '-space-x-2',
    loose: '-space-x-1',
  };

  const visibleAvatars = avatars.slice(0, max);
  const remainingCount = Math.max(0, avatars.length - max);

  const sizeClasses = {
    xs: 'w-6 h-6 text-xs',
    sm: 'w-8 h-8 text-sm',
    md: 'w-10 h-10 text-base',
    lg: 'w-12 h-12 text-lg',
    xl: 'w-16 h-16 text-xl',
    '2xl': 'w-20 h-20 text-2xl',
  };

  const shapeClasses = {
    circle: 'rounded-full',
    square: 'rounded-none',
    rounded: 'rounded-lg',
  };

  return (
    <div
      className={cn(
        'flex items-center',
        spacingClasses[spacing],
        className
      )}
      data-testid={testId}
    >
      {visibleAvatars.map((avatar, index) => (
        <div
          key={index}
          className={cn(
            'relative',
            showBorder && 'ring-2 ring-white dark:ring-gray-800'
          )}
          style={{ zIndex: visibleAvatars.length - index }}
        >
          <Avatar
            {...avatar}
            size={size}
            shape={shape}
            showBorder={false} // We handle border at group level
          />
        </div>
      ))}
      
      {remainingCount > 0 && (
        <div
          className={cn(
            'relative flex items-center justify-center font-medium text-white',
            'bg-gray-500 hover:bg-gray-600 transition-colors',
            sizeClasses[size],
            shapeClasses[shape],
            showBorder && 'ring-2 ring-white dark:ring-gray-800',
            onMoreClick && 'cursor-pointer'
          )}
          style={{ zIndex: 0 }}
          onClick={onMoreClick}
          title={`${remainingCount} more`}
        >
          +{remainingCount}
        </div>
      )}
    </div>
  );
};
