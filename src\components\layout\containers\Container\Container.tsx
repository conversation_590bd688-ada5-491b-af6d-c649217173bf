import React from 'react';
import { cn } from '../../../../utils/cn';
import type { ContainerProps } from '../../../types/layout';

/**
 * Container component for consistent content width and centering
 * Provides responsive max-width constraints and optional centering
 */
export const Container: React.FC<ContainerProps & { children: React.ReactNode }> = ({
  children,
  maxWidth = 'xl',
  centered = true,
  fluid = false,
  padding,
  margin,
  className = '',
  'data-testid': testId,
}) => {
  const maxWidthClasses = {
    xs: 'max-w-xs',
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
  };

  const paddingClasses = {
    0: 'p-0',
    1: 'p-1',
    2: 'p-2',
    3: 'p-3',
    4: 'p-4',
    5: 'p-5',
    6: 'p-6',
    8: 'p-8',
    10: 'p-10',
    12: 'p-12',
    16: 'p-16',
    20: 'p-20',
    24: 'p-24',
  };

  const marginClasses = {
    0: 'm-0',
    1: 'm-1',
    2: 'm-2',
    3: 'm-3',
    4: 'm-4',
    5: 'm-5',
    6: 'm-6',
    8: 'm-8',
    10: 'm-10',
    12: 'm-12',
    16: 'm-16',
    20: 'm-20',
    24: 'm-24',
  };

  const containerClasses = cn(
    'w-full',
    !fluid && typeof maxWidth === 'string' && maxWidthClasses[maxWidth as keyof typeof maxWidthClasses],
    centered && 'mx-auto',
    typeof padding === 'number' && paddingClasses[padding as keyof typeof paddingClasses],
    typeof margin === 'number' && marginClasses[margin as keyof typeof marginClasses],
    className
  );

  return (
    <div
      className={containerClasses}
      data-testid={testId}
    >
      {children}
    </div>
  );
};
