// Main Components Export
// This file provides a centralized export for all component categories
// Import components by category for better tree-shaking and organization
//
// 🏗️ Component Architecture Overview:
//
// Our component system follows industry standards with clear categorization:
// - Atomic design principles (atoms, molecules, organisms)
// - Separation of concerns (layout, data, interaction)
// - Consistent theming and accessibility
// - TypeScript-first with comprehensive type definitions
//
// 📦 Import Strategy:
// ```typescript
// // Recommended: Import by category for better tree-shaking
// import { Button, Input } from '../components/forms';
// import { Card, Text } from '../components/global';
// import { DataTable, Badge } from '../components/data-display';
//
// // Avoid: Importing everything (increases bundle size)
// import * from '../components';
// ```
//
// 🎨 Theming:
// All components use useThemeStore for consistent theming across the application.
// Colors, spacing, and typography follow the design system principles.
//
// ♿ Accessibility:
// Components are built with WCAG 2.1 AA compliance in mind, including:
// - Proper ARIA attributes
// - Keyboard navigation support
// - Screen reader compatibility
// - Focus management

// Global Components - Highly reusable components used across modules
export * from './global';

// UI Components - Basic building blocks (some deprecated, use global/ or forms/ instead)
export * from './ui';

// Layout Components - Structural components
export * from './layout';

// Form Components - Form and input handling
export * from './forms';

// Input Components - All input types, buttons, and selectors
export * from './inputs';

// Navigation Components - Navigation and routing
export * from './navigation';

// Feedback Components - User feedback and notifications
export * from './feedback';

// Data Display Components - Data presentation
export * from './data-display';

// Chart Components - Data visualization
export * from './charts';

// Views Components - All view types for data display
export * from './views';

// Common Components - Shared utilities
export * from './common';

// Icon Components - Professional single-color icons
export * from './icons';

// Dashboard Components - Main dashboard interface
export * from './Dashboard';

// Authentication Components - Login, registration, access control
export * from './auth';

// Security Components - CAPTCHA, verification, etc.
export * from './security';

// User Management Module (migrated to modules/user-management)
// Import from: import { AddUserModal, UserList } from '../modules/user-management';

// 📚 Component Usage Examples:
//
// Data Display:
// ```typescript
// import { DataTable, Badge, Chip, InfoCard, StatsCard } from '../components/data-display';
// import { Timeline, TimelineItem, ActivityFeed } from '../components/data-display';
// ```
//
// Navigation:
// ```typescript
// import { Menu, ContextMenu, NavigationMenu } from '../components/navigation';
// import { Stepper, Pagination, PageSizeSelector } from '../components/navigation';
// import { Link, NavLink } from '../components/navigation';
// ```
//
// Global Components (Highly Recommended):
// ```typescript
// import { Card, Text, Heading, Separator } from '../components/global';
// import { ViewModeSelector, Dropdown, FilterDropdown } from '../components/global';
// import { Breadcrumb, AppNotFound } from '../components/global';
// ```
//
// Forms and Inputs:
// ```typescript
// import { Button, Input, Label } from '../components/forms';
// import { TextArea, Select, DatePicker } from '../components/inputs';
// ```
//
// 🔄 Migration Guide:
//
// Deprecated → Recommended:
// - ui/Card → global/Card
// - ui/Text → global/Text
// - ui/ViewModeSwitcher → global/ViewModeSelector
// - ui/Dropdown → global/Dropdown
//
// For new components, always check global/ first, then category-specific folders.

// 🏷️ Component Categories Summary:
//
// ✅ Fully Implemented:
// - Global: Core reusable components (Card, Text, Dropdown, ViewModeSelector)
// - Data Display: Tables, lists, cards, badges, timeline, activity feeds
// - Navigation: Menus, breadcrumbs, pagination, steppers, links
// - Forms: Buttons, inputs, labels with validation
// - Layout: Headers, sidebars, containers, responsive components
// - Feedback: Modals, notifications, alerts, loading states
//
// 🚧 Partially Implemented:
// - Inputs: Basic inputs complete, advanced inputs in progress
// - Views: Core views implemented, specialized views in development
// - Charts: Structure ready, chart implementations pending
//
// 📋 Planned:
// - Authentication: Login, registration, access control
// - Security: CAPTCHA, verification components
// - Icons: Professional icon library
