import React from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../utils/cn';
import type { MainContentProps } from '../../../types/layout';

/**
 * MainContent component for primary content areas
 * Provides consistent styling and layout for main application content
 */
export const MainContent: React.FC<MainContentProps & { children: React.ReactNode }> = ({
  children,
  variant = 'default',
  scrollable = true,
  fullHeight = true,
  withSidebar = false,
  sidebarPosition = 'left',
  padding,
  margin,
  gap,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const paddingClasses = {
    0: 'p-0',
    1: 'p-1',
    2: 'p-2',
    3: 'p-3',
    4: 'p-4',
    5: 'p-5',
    6: 'p-6',
    8: 'p-8',
    10: 'p-10',
    12: 'p-12',
    16: 'p-16',
    20: 'p-20',
    24: 'p-24',
  };

  const marginClasses = {
    0: 'm-0',
    1: 'm-1',
    2: 'm-2',
    3: 'm-3',
    4: 'm-4',
    5: 'm-5',
    6: 'm-6',
    8: 'm-8',
    10: 'm-10',
    12: 'm-12',
    16: 'm-16',
    20: 'm-20',
    24: 'm-24',
  };

  const variantClasses = {
    default: 'p-6',
    compact: 'p-4',
    minimal: 'p-2',
    expanded: 'p-8',
  };

  const mainClasses = cn(
    'flex-1',
    fullHeight && 'min-h-full',
    scrollable && 'overflow-auto',
    !scrollable && 'overflow-hidden',
    withSidebar && sidebarPosition === 'left' && 'ml-0',
    withSidebar && sidebarPosition === 'right' && 'mr-0',
    variantClasses[variant],
    typeof padding === 'number' && paddingClasses[padding as keyof typeof paddingClasses],
    typeof margin === 'number' && marginClasses[margin as keyof typeof marginClasses],
    className
  );

  return (
    <main
      className={mainClasses}
      style={{ backgroundColor: colors.background }}
      data-testid={testId}
    >
      {children}
    </main>
  );
};
