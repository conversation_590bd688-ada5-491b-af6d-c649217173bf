import React from 'react';
import { cn } from '../../../../utils/cn';
import type { BaseLayoutProps, LayoutSpacing, LayoutAlignment } from '../../../types/layout';

export interface StackProps extends BaseLayoutProps {
  children: React.ReactNode;
  direction?: 'vertical' | 'horizontal';
  spacing?: LayoutSpacing;
  align?: LayoutAlignment;
  divider?: React.ReactNode;
}

/**
 * Stack component for consistent spacing between child elements
 * Provides vertical or horizontal stacking with optional dividers
 */
export const Stack: React.FC<StackProps> = ({
  children,
  direction = 'vertical',
  spacing = 4,
  align = 'stretch',
  divider,
  className = '',
  'data-testid': testId,
}) => {
  const spacingClasses = {
    0: 'gap-0',
    1: 'gap-1',
    2: 'gap-2',
    3: 'gap-3',
    4: 'gap-4',
    5: 'gap-5',
    6: 'gap-6',
    8: 'gap-8',
    10: 'gap-10',
    12: 'gap-12',
    16: 'gap-16',
    20: 'gap-20',
    24: 'gap-24',
  };

  const alignClasses = {
    start: direction === 'vertical' ? 'items-start' : 'justify-start',
    center: direction === 'vertical' ? 'items-center' : 'justify-center',
    end: direction === 'vertical' ? 'items-end' : 'justify-end',
    stretch: direction === 'vertical' ? 'items-stretch' : 'justify-stretch',
    baseline: direction === 'vertical' ? 'items-baseline' : 'justify-baseline',
  };

  const stackClasses = cn(
    'flex',
    direction === 'vertical' ? 'flex-col' : 'flex-row',
    spacingClasses[spacing as keyof typeof spacingClasses],
    alignClasses[align],
    className
  );

  const childrenArray = React.Children.toArray(children);

  if (divider && childrenArray.length > 1) {
    const childrenWithDividers: React.ReactNode[] = [];
    
    childrenArray.forEach((child, index) => {
      childrenWithDividers.push(child);
      if (index < childrenArray.length - 1) {
        childrenWithDividers.push(
          <div key={`divider-${index}`} className="flex-shrink-0">
            {divider}
          </div>
        );
      }
    });

    return (
      <div className={stackClasses} data-testid={testId}>
        {childrenWithDividers}
      </div>
    );
  }

  return (
    <div className={stackClasses} data-testid={testId}>
      {children}
    </div>
  );
};
