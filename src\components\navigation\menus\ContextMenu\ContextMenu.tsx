import React, { useState, useRef, useEffect } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../utils/cn';
import type { MenuItem } from '../Menu/Menu';

export interface ContextMenuProps {
  children: React.ReactNode;
  items: MenuItem[];
  disabled?: boolean;
  className?: string;
  'data-testid'?: string;
}

export const ContextMenu: React.FC<ContextMenuProps> = ({
  children,
  items,
  disabled = false,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const [isOpen, setIsOpen] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const menuRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    const handleScroll = () => {
      setIsOpen(false);
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('scroll', handleScroll, true);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('scroll', handleScroll, true);
    };
  }, [isOpen]);

  const handleContextMenu = (event: React.MouseEvent) => {
    if (disabled) return;
    
    event.preventDefault();
    event.stopPropagation();

    const rect = containerRef.current?.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const menuWidth = 200; // Approximate menu width
    const menuHeight = items.length * 40; // Approximate menu height

    let x = event.clientX;
    let y = event.clientY;

    // Adjust position if menu would go off-screen
    if (x + menuWidth > viewportWidth) {
      x = viewportWidth - menuWidth - 10;
    }
    if (y + menuHeight > viewportHeight) {
      y = viewportHeight - menuHeight - 10;
    }

    setPosition({ x, y });
    setIsOpen(true);
  };

  const handleItemClick = (item: MenuItem) => {
    if (item.disabled) return;

    if (item.onClick) {
      item.onClick();
    }
    setIsOpen(false);
  };

  const renderMenuItem = (item: MenuItem) => {
    if (item.divider) {
      return (
        <div
          key={item.id}
          className="my-1 border-t"
          style={{ borderColor: colors.border }}
        />
      );
    }

    return (
      <button
        key={item.id}
        className={cn(
          'w-full flex items-center px-3 py-2 text-sm text-left',
          'hover:bg-opacity-50 transition-colors',
          item.disabled && 'opacity-50 cursor-not-allowed'
        )}
        style={{
          color: item.disabled ? colors.mutedForeground : colors.text,
          backgroundColor: 'transparent',
        }}
        onMouseEnter={e => {
          if (!item.disabled) {
            e.currentTarget.style.backgroundColor = colors.hover + '15';
          }
        }}
        onMouseLeave={e => {
          e.currentTarget.style.backgroundColor = 'transparent';
        }}
        onClick={() => handleItemClick(item)}
        disabled={item.disabled}
      >
        {item.icon && (
          <span className="mr-2 flex-shrink-0">{item.icon}</span>
        )}
        <span>{item.label}</span>
      </button>
    );
  };

  return (
    <>
      <div
        ref={containerRef}
        className={cn('cursor-context-menu', className)}
        onContextMenu={handleContextMenu}
        data-testid={testId}
      >
        {children}
      </div>

      {isOpen && (
        <div
          ref={menuRef}
          className="fixed z-50 min-w-48 py-2 rounded-lg border shadow-lg"
          style={{
            left: position.x,
            top: position.y,
            backgroundColor: colors.surface,
            borderColor: colors.border,
          }}
        >
          {items.map(item => renderMenuItem(item))}
        </div>
      )}
    </>
  );
};
