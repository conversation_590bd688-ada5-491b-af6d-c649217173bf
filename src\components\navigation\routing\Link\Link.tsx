import React from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../utils/cn';

export interface LinkProps {
  children: React.ReactNode;
  href?: string;
  to?: string; // For React Router compatibility
  variant?: 'default' | 'primary' | 'secondary' | 'muted' | 'destructive';
  size?: 'sm' | 'md' | 'lg';
  underline?: 'none' | 'hover' | 'always';
  external?: boolean;
  disabled?: boolean;
  onClick?: (event: React.MouseEvent<HTMLAnchorElement>) => void;
  className?: string;
  'data-testid'?: string;
}

export const Link: React.FC<LinkProps> = ({
  children,
  href,
  to,
  variant = 'default',
  size = 'md',
  underline = 'hover',
  external = false,
  disabled = false,
  onClick,
  className = '',
  'data-testid': testId,
  ...props
}) => {
  const { colors } = useThemeStore();

  const sizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
  };

  const underlineClasses = {
    none: 'no-underline',
    hover: 'no-underline hover:underline',
    always: 'underline',
  };

  const variantStyles = {
    default: {
      color: colors.primary,
      hoverColor: colors.primary,
    },
    primary: {
      color: colors.primary,
      hoverColor: colors.primary,
    },
    secondary: {
      color: colors.textSecondary,
      hoverColor: colors.text,
    },
    muted: {
      color: colors.mutedForeground,
      hoverColor: colors.text,
    },
    destructive: {
      color: '#ef4444',
      hoverColor: '#dc2626',
    },
  };

  const currentStyle = variantStyles[variant];
  const linkUrl = href || to;

  const handleClick = (event: React.MouseEvent<HTMLAnchorElement>) => {
    if (disabled) {
      event.preventDefault();
      return;
    }

    onClick?.(event);
  };

  const linkProps = {
    ...props,
    href: linkUrl,
    onClick: handleClick,
    className: cn(
      'transition-colors duration-200 cursor-pointer',
      sizeClasses[size],
      underlineClasses[underline],
      disabled && 'opacity-50 cursor-not-allowed pointer-events-none',
      className
    ),
    style: {
      color: disabled ? colors.mutedForeground : currentStyle.color,
    },
    'data-testid': testId,
  };

  // Add external link attributes
  if (external && linkUrl) {
    linkProps.target = '_blank';
    linkProps.rel = 'noopener noreferrer';
  }

  return (
    <a
      {...linkProps}
      onMouseEnter={e => {
        if (!disabled) {
          e.currentTarget.style.color = currentStyle.hoverColor;
        }
      }}
      onMouseLeave={e => {
        if (!disabled) {
          e.currentTarget.style.color = currentStyle.color;
        }
      }}
    >
      {children}
      {external && (
        <span className="ml-1 inline-block">
          ↗
        </span>
      )}
    </a>
  );
};
