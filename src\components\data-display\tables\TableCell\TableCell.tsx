import React from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../utils/cn';

export interface TableCellProps {
  children: React.ReactNode;
  align?: 'left' | 'center' | 'right';
  width?: string | number;
  padding?: 'sm' | 'md' | 'lg';
  truncate?: boolean;
  className?: string;
  'data-testid'?: string;
}

export const TableCell: React.FC<TableCellProps> = ({
  children,
  align = 'left',
  width,
  padding = 'md',
  truncate = false,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const alignClasses = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right',
  };

  const paddingClasses = {
    sm: 'px-2 py-2',
    md: 'px-4 py-3',
    lg: 'px-6 py-4',
  };

  return (
    <td
      className={cn(
        paddingClasses[padding],
        alignClasses[align],
        truncate && 'truncate max-w-0',
        className
      )}
      style={{
        color: colors.text,
        width: width,
      }}
      data-testid={testId}
    >
      {truncate ? (
        <div className="truncate" title={typeof children === 'string' ? children : undefined}>
          {children}
        </div>
      ) : (
        children
      )}
    </td>
  );
};
