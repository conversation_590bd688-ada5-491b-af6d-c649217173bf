import React from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../utils/cn';

export interface TableProps {
  children: React.ReactNode;
  variant?: 'default' | 'striped' | 'bordered' | 'minimal';
  size?: 'sm' | 'md' | 'lg';
  hoverable?: boolean;
  stickyHeader?: boolean;
  className?: string;
  'data-testid'?: string;
}

export const Table: React.FC<TableProps> = ({
  children,
  variant = 'default',
  size = 'md',
  hoverable = true,
  stickyHeader = false,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const sizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
  };

  const variantClasses = {
    default: 'border-collapse',
    striped: 'border-collapse',
    bordered: 'border border-collapse',
    minimal: 'border-collapse',
  };

  return (
    <div className="overflow-x-auto">
      <table
        className={cn(
          'w-full',
          sizeClasses[size],
          variantClasses[variant],
          stickyHeader && 'sticky-header',
          className
        )}
        style={{
          backgroundColor: colors.background,
          color: colors.text,
          borderColor: colors.border,
        }}
        data-testid={testId}
      >
        {children}
      </table>
    </div>
  );
};
