// Content Layout Components
// Main content areas, panels, and content organization

// Dynamic Content
export { default as AppDynamicContent } from './AppDynamicContent';
export type { AppDynamicContentProps } from './AppDynamicContent';

// Core Content Components
export { MainContent } from './MainContent';
export type { MainContentProps } from './MainContent';

export { Section } from './Section';
export type { SectionProps } from './Section';

// Layout Utilities (re-exported from global for convenience)
export { Separator } from '../../global/Separator';
export type { SeparatorProps } from '../../global/Separator';

// TODO: Add more content components
// export { default as ContentArea } from './ContentArea';
// export { default as Panel } from './Panel';
