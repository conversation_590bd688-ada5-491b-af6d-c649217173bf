import React from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../utils/cn';

export interface AlertProps {
  title?: string;
  children: React.ReactNode;
  variant?: 'info' | 'success' | 'warning' | 'error';
  size?: 'sm' | 'md' | 'lg';
  dismissible?: boolean;
  onDismiss?: () => void;
  icon?: React.ReactNode;
  actions?: React.ReactNode;
  className?: string;
  'data-testid'?: string;
}

export const Alert: React.FC<AlertProps> = ({
  title,
  children,
  variant = 'info',
  size = 'md',
  dismissible = false,
  onDismiss,
  icon,
  actions,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const variantStyles = {
    info: {
      backgroundColor: colors.primary + '10',
      borderColor: colors.primary + '30',
      iconColor: colors.primary,
    },
    success: {
      backgroundColor: '#10b981' + '10',
      borderColor: '#10b981' + '30',
      iconColor: '#10b981',
    },
    warning: {
      backgroundColor: '#f59e0b' + '10',
      borderColor: '#f59e0b' + '30',
      iconColor: '#f59e0b',
    },
    error: {
      backgroundColor: '#ef4444' + '10',
      borderColor: '#ef4444' + '30',
      iconColor: '#ef4444',
    },
  };

  const sizeClasses = {
    sm: 'p-3 text-sm',
    md: 'p-4 text-base',
    lg: 'p-6 text-lg',
  };

  const defaultIcons = {
    info: '🛈',
    success: '✓',
    warning: '⚠',
    error: '✕',
  };

  const currentStyle = variantStyles[variant];
  const displayIcon = icon || defaultIcons[variant];

  return (
    <div
      className={cn(
        'border rounded-lg',
        sizeClasses[size],
        className
      )}
      style={{
        backgroundColor: currentStyle.backgroundColor,
        borderColor: currentStyle.borderColor,
      }}
      data-testid={testId}
    >
      <div className="flex items-start gap-3">
        {/* Icon */}
        {displayIcon && (
          <div
            className="flex-shrink-0 w-5 h-5 flex items-center justify-center text-sm font-bold"
            style={{ color: currentStyle.iconColor }}
          >
            {displayIcon}
          </div>
        )}

        {/* Content */}
        <div className="flex-1 min-w-0">
          {title && (
            <div
              className="font-semibold mb-1"
              style={{ color: colors.text }}
            >
              {title}
            </div>
          )}
          <div style={{ color: colors.text }}>
            {children}
          </div>
        </div>

        {/* Actions */}
        {actions && (
          <div className="flex-shrink-0">
            {actions}
          </div>
        )}

        {/* Dismiss Button */}
        {dismissible && (
          <button
            onClick={onDismiss}
            className="flex-shrink-0 w-5 h-5 flex items-center justify-center text-sm opacity-60 hover:opacity-100 transition-opacity"
            style={{ color: colors.mutedForeground }}
            aria-label="Dismiss"
          >
            ✕
          </button>
        )}
      </div>
    </div>
  );
};
