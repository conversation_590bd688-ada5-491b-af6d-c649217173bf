import React from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../utils/cn';

export interface StepperStep {
  id: string;
  title: string;
  description?: string;
  icon?: React.ReactNode;
  status: 'pending' | 'current' | 'completed' | 'error';
  optional?: boolean;
}

export interface StepperProps {
  steps: StepperStep[];
  orientation?: 'horizontal' | 'vertical';
  variant?: 'default' | 'minimal' | 'numbered';
  size?: 'sm' | 'md' | 'lg';
  showConnector?: boolean;
  onStepClick?: (step: StepperStep, index: number) => void;
  className?: string;
  'data-testid'?: string;
}

export const Stepper: React.FC<StepperProps> = ({
  steps,
  orientation = 'horizontal',
  variant = 'default',
  size = 'md',
  showConnector = true,
  onStepClick,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const sizeClasses = {
    sm: {
      step: 'w-6 h-6 text-xs',
      title: 'text-sm',
      description: 'text-xs',
    },
    md: {
      step: 'w-8 h-8 text-sm',
      title: 'text-base',
      description: 'text-sm',
    },
    lg: {
      step: 'w-10 h-10 text-base',
      title: 'text-lg',
      description: 'text-base',
    },
  };

  const getStepColor = (status: StepperStep['status']) => {
    switch (status) {
      case 'completed':
        return '#10b981';
      case 'current':
        return colors.primary;
      case 'error':
        return '#ef4444';
      case 'pending':
      default:
        return colors.mutedForeground;
    }
  };

  const getStepIcon = (step: StepperStep, index: number) => {
    if (step.icon) return step.icon;
    
    switch (step.status) {
      case 'completed':
        return '✓';
      case 'error':
        return '✕';
      case 'current':
      case 'pending':
      default:
        return variant === 'numbered' ? index + 1 : '●';
    }
  };

  const renderConnector = (index: number) => {
    if (!showConnector || index === steps.length - 1) return null;

    const nextStep = steps[index + 1];
    const isCompleted = steps[index].status === 'completed';
    
    return (
      <div
        className={cn(
          'flex-1',
          orientation === 'horizontal' ? 'h-0.5 mx-2' : 'w-0.5 my-2 ml-4'
        )}
        style={{
          backgroundColor: isCompleted ? '#10b981' : colors.border,
        }}
      />
    );
  };

  const renderStep = (step: StepperStep, index: number) => {
    const stepColor = getStepColor(step.status);
    const isClickable = !!onStepClick && step.status !== 'pending';

    return (
      <div
        key={step.id}
        className={cn(
          'flex items-center',
          orientation === 'vertical' && 'flex-col items-start',
          isClickable && 'cursor-pointer'
        )}
        onClick={isClickable ? () => onStepClick!(step, index) : undefined}
      >
        {/* Step indicator */}
        <div
          className={cn(
            'flex items-center justify-center rounded-full border-2 font-medium',
            sizeClasses[size].step,
            step.status === 'current' && 'ring-2 ring-opacity-50'
          )}
          style={{
            backgroundColor: step.status === 'completed' || step.status === 'current' 
              ? stepColor 
              : colors.surface,
            borderColor: stepColor,
            color: step.status === 'completed' || step.status === 'current' 
              ? '#ffffff' 
              : stepColor,
            ringColor: step.status === 'current' ? stepColor : 'transparent',
          }}
        >
          {getStepIcon(step, index)}
        </div>

        {/* Step content */}
        {variant !== 'minimal' && (
          <div
            className={cn(
              'ml-3',
              orientation === 'vertical' && 'ml-0 mt-2'
            )}
          >
            <div
              className={cn(
                'font-medium',
                sizeClasses[size].title
              )}
              style={{
                color: step.status === 'current' ? stepColor : colors.text,
              }}
            >
              {step.title}
              {step.optional && (
                <span
                  className="ml-1 text-xs font-normal"
                  style={{ color: colors.mutedForeground }}
                >
                  (optional)
                </span>
              )}
            </div>
            
            {step.description && (
              <div
                className={cn(
                  'mt-1',
                  sizeClasses[size].description
                )}
                style={{ color: colors.textSecondary }}
              >
                {step.description}
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  return (
    <div
      className={cn(
        'flex',
        orientation === 'horizontal' ? 'items-center' : 'flex-col',
        className
      )}
      data-testid={testId}
    >
      {steps.map((step, index) => (
        <React.Fragment key={step.id}>
          {renderStep(step, index)}
          {renderConnector(index)}
        </React.Fragment>
      ))}
    </div>
  );
};
