import React from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../utils/cn';
import { Sidebar } from '../Sidebar';
import type { SidebarProps } from '../../../types/layout';

export interface NavigationItem {
  id: string;
  label: string;
  icon?: React.ReactNode;
  href?: string;
  onClick?: () => void;
  active?: boolean;
  disabled?: boolean;
  children?: NavigationItem[];
}

export interface NavigationSidebarProps extends Omit<SidebarProps, 'children'> {
  items: NavigationItem[];
  onItemClick?: (item: NavigationItem) => void;
  showIcons?: boolean;
  compact?: boolean;
}

/**
 * NavigationSidebar component for app navigation
 * Provides structured navigation with icons and nested items
 */
export const NavigationSidebar: React.FC<NavigationSidebarProps> = ({
  items,
  onItemClick,
  showIcons = true,
  compact = false,
  collapsed = false,
  ...sidebarProps
}) => {
  const { colors } = useThemeStore();

  const renderNavigationItem = (item: NavigationItem, level = 0) => {
    const itemClasses = cn(
      'flex items-center px-3 py-2 text-sm rounded-md transition-colors duration-200',
      'hover:bg-opacity-10 hover:bg-gray-500',
      item.active && 'bg-opacity-20 bg-blue-500 text-blue-600 font-medium',
      item.disabled && 'opacity-50 cursor-not-allowed',
      level > 0 && 'ml-4',
      collapsed && showIcons && 'justify-center px-2'
    );

    const handleClick = () => {
      if (!item.disabled) {
        item.onClick?.();
        onItemClick?.(item);
      }
    };

    return (
      <div key={item.id}>
        <button
          className={itemClasses}
          onClick={handleClick}
          disabled={item.disabled}
          style={{ color: item.active ? colors.primary : colors.text }}
        >
          {showIcons && item.icon && (
            <span className={cn('flex-shrink-0', !collapsed && 'mr-3')}>
              {item.icon}
            </span>
          )}
          {!collapsed && (
            <span className="flex-1 text-left">{item.label}</span>
          )}
        </button>
        
        {!collapsed && item.children && item.children.length > 0 && (
          <div className="mt-1">
            {item.children.map(child => renderNavigationItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <Sidebar {...sidebarProps} collapsed={collapsed}>
      <nav className="flex-1 px-2 py-4 space-y-1">
        {items.map(item => renderNavigationItem(item))}
      </nav>
    </Sidebar>
  );
};
