import React from 'react';
import { cn } from '../../../../utils/cn';
import type { FlexLayoutProps } from '../../../types/layout';

/**
 * Flex component for flexible layouts using CSS Flexbox
 * Provides responsive flex properties and spacing controls
 */
export const Flex: React.FC<FlexLayoutProps & { children: React.ReactNode }> = ({
  children,
  direction = 'row',
  align = 'stretch',
  justify = 'start',
  wrap = false,
  flex,
  gap,
  padding,
  margin,
  className = '',
  'data-testid': testId,
}) => {
  const directionClasses = {
    row: 'flex-row',
    column: 'flex-col',
    'row-reverse': 'flex-row-reverse',
    'column-reverse': 'flex-col-reverse',
  };

  const alignClasses = {
    start: 'items-start',
    center: 'items-center',
    end: 'items-end',
    stretch: 'items-stretch',
    baseline: 'items-baseline',
  };

  const justifyClasses = {
    start: 'justify-start',
    center: 'justify-center',
    end: 'justify-end',
    between: 'justify-between',
    around: 'justify-around',
    evenly: 'justify-evenly',
  };

  const gapClasses = {
    0: 'gap-0',
    1: 'gap-1',
    2: 'gap-2',
    3: 'gap-3',
    4: 'gap-4',
    5: 'gap-5',
    6: 'gap-6',
    8: 'gap-8',
    10: 'gap-10',
    12: 'gap-12',
    16: 'gap-16',
    20: 'gap-20',
    24: 'gap-24',
  };

  const paddingClasses = {
    0: 'p-0',
    1: 'p-1',
    2: 'p-2',
    3: 'p-3',
    4: 'p-4',
    5: 'p-5',
    6: 'p-6',
    8: 'p-8',
    10: 'p-10',
    12: 'p-12',
    16: 'p-16',
    20: 'p-20',
    24: 'p-24',
  };

  const marginClasses = {
    0: 'm-0',
    1: 'm-1',
    2: 'm-2',
    3: 'm-3',
    4: 'm-4',
    5: 'm-5',
    6: 'm-6',
    8: 'm-8',
    10: 'm-10',
    12: 'm-12',
    16: 'm-16',
    20: 'm-20',
    24: 'm-24',
  };

  const flexClasses = cn(
    'flex',
    typeof direction === 'string' && directionClasses[direction],
    typeof align === 'string' && alignClasses[align],
    typeof justify === 'string' && justifyClasses[justify],
    wrap && 'flex-wrap',
    typeof gap === 'number' && gapClasses[gap as keyof typeof gapClasses],
    typeof padding === 'number' && paddingClasses[padding as keyof typeof paddingClasses],
    typeof margin === 'number' && marginClasses[margin as keyof typeof marginClasses],
    className
  );

  const flexStyle = typeof flex === 'string' || typeof flex === 'number' ? { flex } : undefined;

  return (
    <div
      className={flexClasses}
      style={flexStyle}
      data-testid={testId}
    >
      {children}
    </div>
  );
};
