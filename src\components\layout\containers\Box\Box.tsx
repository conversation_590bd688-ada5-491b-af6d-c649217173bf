import React from 'react';
import { cn } from '../../../../utils/cn';
import type { BaseLayoutProps, SpacingProps } from '../../../types/layout';

export interface BoxProps extends BaseLayoutProps, SpacingProps {
  children: React.ReactNode;
  as?: keyof JSX.IntrinsicElements;
}

/**
 * Box component for general-purpose layout container
 * Provides spacing controls and semantic HTML element selection
 */
export const Box: React.FC<BoxProps> = ({
  children,
  as: Component = 'div',
  padding,
  margin,
  gap,
  className = '',
  'data-testid': testId,
}) => {
  const paddingClasses = {
    0: 'p-0',
    1: 'p-1',
    2: 'p-2',
    3: 'p-3',
    4: 'p-4',
    5: 'p-5',
    6: 'p-6',
    8: 'p-8',
    10: 'p-10',
    12: 'p-12',
    16: 'p-16',
    20: 'p-20',
    24: 'p-24',
  };

  const marginClasses = {
    0: 'm-0',
    1: 'm-1',
    2: 'm-2',
    3: 'm-3',
    4: 'm-4',
    5: 'm-5',
    6: 'm-6',
    8: 'm-8',
    10: 'm-10',
    12: 'm-12',
    16: 'm-16',
    20: 'm-20',
    24: 'm-24',
  };

  const gapClasses = {
    0: 'gap-0',
    1: 'gap-1',
    2: 'gap-2',
    3: 'gap-3',
    4: 'gap-4',
    5: 'gap-5',
    6: 'gap-6',
    8: 'gap-8',
    10: 'gap-10',
    12: 'gap-12',
    16: 'gap-16',
    20: 'gap-20',
    24: 'gap-24',
  };

  const boxClasses = cn(
    typeof padding === 'number' && paddingClasses[padding as keyof typeof paddingClasses],
    typeof margin === 'number' && marginClasses[margin as keyof typeof marginClasses],
    typeof gap === 'number' && gapClasses[gap as keyof typeof gapClasses],
    className
  );

  return (
    <Component className={boxClasses} data-testid={testId}>
      {children}
    </Component>
  );
};
