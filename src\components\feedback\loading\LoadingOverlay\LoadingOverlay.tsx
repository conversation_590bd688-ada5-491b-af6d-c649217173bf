import React from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../utils/cn';
import { Spinner } from '../Spinner';

export interface LoadingOverlayProps {
  loading?: boolean;
  message?: string;
  size?: 'sm' | 'md' | 'lg';
  backdrop?: boolean;
  blur?: boolean;
  children?: React.ReactNode;
  className?: string;
  'data-testid'?: string;
}

/**
 * LoadingOverlay component for showing loading state over content
 * Provides a loading spinner with optional backdrop and message
 */
export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  loading = true,
  message,
  size = 'md',
  backdrop = true,
  blur = false,
  children,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const sizeConfig = {
    sm: {
      spinner: 'sm' as const,
      text: 'text-sm',
      gap: 'gap-2',
    },
    md: {
      spinner: 'md' as const,
      text: 'text-base',
      gap: 'gap-3',
    },
    lg: {
      spinner: 'lg' as const,
      text: 'text-lg',
      gap: 'gap-4',
    },
  };

  const config = sizeConfig[size];

  if (!loading && !children) return null;

  return (
    <div className={cn('relative', className)} data-testid={testId}>
      {children}
      
      {loading && (
        <div
          className={cn(
            'absolute inset-0 flex flex-col items-center justify-center z-50',
            backdrop && 'bg-black bg-opacity-50',
            blur && 'backdrop-blur-sm',
            config.gap
          )}
          style={{
            backgroundColor: backdrop ? colors.background + '80' : 'transparent',
          }}
        >
          <Spinner size={config.spinner} />
          
          {message && (
            <p
              className={cn('font-medium', config.text)}
              style={{ color: colors.text }}
            >
              {message}
            </p>
          )}
        </div>
      )}
    </div>
  );
};
