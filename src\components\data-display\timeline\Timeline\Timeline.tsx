import React from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../utils/cn';

export interface TimelineProps {
  children: React.ReactNode;
  variant?: 'default' | 'minimal' | 'detailed';
  orientation?: 'vertical' | 'horizontal';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  'data-testid'?: string;
}

export const Timeline: React.FC<TimelineProps> = ({
  children,
  variant = 'default',
  orientation = 'vertical',
  size = 'md',
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const orientationClasses = {
    vertical: 'flex flex-col',
    horizontal: 'flex flex-row overflow-x-auto',
  };

  const sizeClasses = {
    sm: 'space-y-2',
    md: 'space-y-4',
    lg: 'space-y-6',
  };

  const horizontalSizeClasses = {
    sm: 'space-x-2',
    md: 'space-x-4',
    lg: 'space-x-6',
  };

  return (
    <div
      className={cn(
        'relative',
        orientationClasses[orientation],
        orientation === 'vertical' ? sizeClasses[size] : horizontalSizeClasses[size],
        className
      )}
      data-testid={testId}
    >
      {/* Timeline line */}
      {variant !== 'minimal' && (
        <div
          className={cn(
            'absolute',
            orientation === 'vertical'
              ? 'left-4 top-0 bottom-0 w-0.5'
              : 'top-4 left-0 right-0 h-0.5'
          )}
          style={{ backgroundColor: colors.border }}
        />
      )}
      
      {children}
    </div>
  );
};
