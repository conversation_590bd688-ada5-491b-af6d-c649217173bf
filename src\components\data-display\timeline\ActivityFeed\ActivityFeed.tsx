import React from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../utils/cn';
import { Timeline } from '../Timeline';
import { TimelineItem } from '../TimelineItem';

export interface ActivityItem {
  id: string;
  type: 'create' | 'update' | 'delete' | 'comment' | 'assign' | 'custom';
  title: string;
  description?: string;
  user?: {
    name: string;
    avatar?: string;
  };
  timestamp: string | Date;
  icon?: React.ReactNode;
  metadata?: Record<string, any>;
}

export interface ActivityFeedProps {
  activities: ActivityItem[];
  loading?: boolean;
  emptyMessage?: string;
  showUserAvatars?: boolean;
  groupByDate?: boolean;
  onActivityClick?: (activity: ActivityItem) => void;
  className?: string;
  'data-testid'?: string;
}

export const ActivityFeed: React.FC<ActivityFeedProps> = ({
  activities,
  loading = false,
  emptyMessage = 'No activities yet',
  showUserAvatars = true,
  groupByDate = false,
  onActivityClick,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const getActivityIcon = (activity: ActivityItem) => {
    if (activity.icon) return activity.icon;
    
    const iconMap = {
      create: '➕',
      update: '✏️',
      delete: '🗑️',
      comment: '💬',
      assign: '👤',
      custom: '📝',
    };
    
    return iconMap[activity.type] || '📝';
  };

  const getActivityVariant = (type: ActivityItem['type']) => {
    const variantMap = {
      create: 'success' as const,
      update: 'info' as const,
      delete: 'error' as const,
      comment: 'default' as const,
      assign: 'warning' as const,
      custom: 'default' as const,
    };
    
    return variantMap[type] || 'default';
  };

  const formatDate = (date: string | Date) => {
    const d = date instanceof Date ? date : new Date(date);
    return d.toLocaleDateString();
  };

  const formatTime = (date: string | Date) => {
    const d = date instanceof Date ? date : new Date(date);
    return d.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const groupActivitiesByDate = (activities: ActivityItem[]) => {
    if (!groupByDate) return { ungrouped: activities };
    
    return activities.reduce((groups, activity) => {
      const date = formatDate(activity.timestamp);
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(activity);
      return groups;
    }, {} as Record<string, ActivityItem[]>);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8" data-testid={testId}>
        <div className="flex items-center space-x-2">
          <div
            className="animate-spin rounded-full h-6 w-6 border-b-2"
            style={{ borderColor: colors.primary }}
          />
          <span style={{ color: colors.textSecondary }}>Loading activities...</span>
        </div>
      </div>
    );
  }

  if (!activities || activities.length === 0) {
    return (
      <div className="flex items-center justify-center p-8" data-testid={testId}>
        <div style={{ color: colors.textSecondary }}>{emptyMessage}</div>
      </div>
    );
  }

  const groupedActivities = groupActivitiesByDate(activities);

  return (
    <div className={cn('w-full', className)} data-testid={testId}>
      {groupByDate ? (
        Object.entries(groupedActivities).map(([date, dateActivities]) => (
          <div key={date} className="mb-6">
            <h3
              className="text-sm font-medium mb-3 px-2"
              style={{ color: colors.textSecondary }}
            >
              {date}
            </h3>
            <Timeline>
              {dateActivities.map((activity, index) => (
                <TimelineItem
                  key={activity.id}
                  title={activity.title}
                  timestamp={formatTime(activity.timestamp)}
                  icon={getActivityIcon(activity)}
                  variant={getActivityVariant(activity.type)}
                  last={index === dateActivities.length - 1}
                  className={onActivityClick ? 'cursor-pointer hover:bg-opacity-50' : ''}
                  onClick={() => onActivityClick?.(activity)}
                >
                  <div className="space-y-1">
                    {activity.user && (
                      <div className="flex items-center space-x-2 text-sm">
                        {showUserAvatars && activity.user.avatar && (
                          <img
                            src={activity.user.avatar}
                            alt={activity.user.name}
                            className="w-4 h-4 rounded-full"
                          />
                        )}
                        <span style={{ color: colors.text }}>
                          {activity.user.name}
                        </span>
                      </div>
                    )}
                    
                    {activity.description && (
                      <p className="text-sm">{activity.description}</p>
                    )}
                  </div>
                </TimelineItem>
              ))}
            </Timeline>
          </div>
        ))
      ) : (
        <Timeline>
          {activities.map((activity, index) => (
            <TimelineItem
              key={activity.id}
              title={activity.title}
              timestamp={activity.timestamp}
              icon={getActivityIcon(activity)}
              variant={getActivityVariant(activity.type)}
              last={index === activities.length - 1}
              className={onActivityClick ? 'cursor-pointer hover:bg-opacity-50' : ''}
              onClick={() => onActivityClick?.(activity)}
            >
              <div className="space-y-1">
                {activity.user && (
                  <div className="flex items-center space-x-2 text-sm">
                    {showUserAvatars && activity.user.avatar && (
                      <img
                        src={activity.user.avatar}
                        alt={activity.user.name}
                        className="w-4 h-4 rounded-full"
                      />
                    )}
                    <span style={{ color: colors.text }}>
                      {activity.user.name}
                    </span>
                  </div>
                )}
                
                {activity.description && (
                  <p className="text-sm">{activity.description}</p>
                )}
              </div>
            </TimelineItem>
          ))}
        </Timeline>
      )}
    </div>
  );
};
