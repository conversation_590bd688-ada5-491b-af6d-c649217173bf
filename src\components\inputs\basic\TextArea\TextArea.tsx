import React, { forwardRef } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../utils/cn';
import type { FormControlProps } from '../../../forms/types';

export interface TextAreaProps
  extends Omit<React.TextareaHTMLAttributes<HTMLTextAreaElement>, 'onChange'>,
    FormControlProps {
  rows?: number;
  resize?: 'none' | 'vertical' | 'horizontal' | 'both';
  autoResize?: boolean;
  maxLength?: number;
  showCharCount?: boolean;
}

/**
 * TextArea component for forms
 * Provides multi-line text input with auto-resize and character count features
 */
export const TextArea = forwardRef<HTMLTextAreaElement, TextAreaProps>(
  (
    {
      label,
      error,
      helperText,
      validationState = 'default',
      size = 'md',
      variant = 'default',
      fullWidth = false,
      rows = 3,
      resize = 'vertical',
      autoResize = false,
      maxLength,
      showCharCount = false,
      value = '',
      onChange,
      className = '',
      disabled = false,
      required = false,
      'data-testid': testId,
      ...props
    },
    ref
  ) => {
    const { colors } = useThemeStore();

    const sizeClasses = {
      sm: 'px-3 py-1.5 text-sm',
      md: 'px-4 py-2 text-base',
      lg: 'px-4 py-3 text-lg',
    };

    const resizeClasses = {
      none: 'resize-none',
      vertical: 'resize-y',
      horizontal: 'resize-x',
      both: 'resize',
    };

    const baseTextAreaClasses =
      'w-full rounded-lg border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-1 disabled:opacity-50 disabled:cursor-not-allowed';

    const variantStyles = (() => {
      const baseStyle = {
        backgroundColor: colors.background,
        color: colors.text,
        borderColor: error ? colors.error : colors.border,
      };

      switch (variant) {
        case 'filled':
          return {
            ...baseStyle,
            backgroundColor: colors.surface,
            borderColor: 'transparent',
          };
        case 'outlined':
          return {
            ...baseStyle,
            backgroundColor: 'transparent',
          };
        default:
          return baseStyle;
      }
    })();

    const focusRingColor = error ? colors.error : colors.primary;

    const textAreaClasses = cn(
      baseTextAreaClasses,
      sizeClasses[size],
      resizeClasses[resize],
      autoResize && 'resize-none',
      className
    );

    const labelClasses = cn(
      'block text-sm font-medium mb-2',
      required && "after:content-['*'] after:ml-0.5 after:text-red-500"
    );

    const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      const newValue = e.target.value;
      
      // Respect maxLength if set
      if (maxLength && newValue.length > maxLength) {
        return;
      }
      
      onChange?.(newValue);

      // Auto-resize functionality
      if (autoResize && e.target) {
        e.target.style.height = 'auto';
        e.target.style.height = `${e.target.scrollHeight}px`;
      }
    };

    const characterCount = typeof value === 'string' ? value.length : 0;
    const isOverLimit = maxLength && characterCount > maxLength;

    return (
      <div className={fullWidth ? 'w-full' : ''}>
        {label && (
          <label
            className={labelClasses}
            style={{ color: error ? colors.error : colors.text }}
          >
            {label}
          </label>
        )}

        <div className="relative">
          <textarea
            ref={ref}
            className={textAreaClasses}
            style={variantStyles}
            disabled={disabled}
            required={required}
            rows={rows}
            value={value}
            onChange={handleChange}
            data-testid={testId}
            {...props}
          />
        </div>

        {/* Character count and helper text */}
        <div className="flex justify-between items-start mt-1">
          <div className="flex-1">
            {error && (
              <p className="text-sm" style={{ color: colors.error }}>
                {error}
              </p>
            )}
            {!error && helperText && (
              <p className="text-sm" style={{ color: colors.textSecondary }}>
                {helperText}
              </p>
            )}
          </div>
          
          {showCharCount && maxLength && (
            <p
              className={`text-xs ml-2 ${isOverLimit ? 'font-medium' : ''}`}
              style={{ 
                color: isOverLimit ? colors.error : colors.textSecondary 
              }}
            >
              {characterCount}/{maxLength}
            </p>
          )}
        </div>
      </div>
    );
  }
);

TextArea.displayName = 'TextArea';
