import React, { useState } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../utils/cn';

export interface NavigationMenuItem {
  id: string;
  label: string;
  icon?: React.ReactNode;
  href?: string;
  onClick?: () => void;
  active?: boolean;
  disabled?: boolean;
  badge?: string | number;
  children?: NavigationMenuItem[];
}

export interface NavigationMenuProps {
  items: NavigationMenuItem[];
  orientation?: 'horizontal' | 'vertical';
  variant?: 'default' | 'pills' | 'underline' | 'minimal';
  size?: 'sm' | 'md' | 'lg';
  showIcons?: boolean;
  collapsible?: boolean;
  onItemClick?: (item: NavigationMenuItem) => void;
  className?: string;
  'data-testid'?: string;
}

export const NavigationMenu: React.FC<NavigationMenuProps> = ({
  items,
  orientation = 'horizontal',
  variant = 'default',
  size = 'md',
  showIcons = true,
  collapsible = false,
  onItemClick,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  const sizeClasses = {
    sm: 'px-2 py-1 text-sm',
    md: 'px-3 py-2 text-base',
    lg: 'px-4 py-3 text-lg',
  };

  const orientationClasses = {
    horizontal: 'flex flex-row space-x-1',
    vertical: 'flex flex-col space-y-1',
  };

  const getItemClasses = (item: NavigationMenuItem, level = 0) => {
    const baseClasses = cn(
      'flex items-center justify-between transition-all duration-200 rounded-md',
      sizeClasses[size],
      item.disabled && 'opacity-50 cursor-not-allowed',
      !item.disabled && 'cursor-pointer',
      level > 0 && 'ml-4'
    );

    switch (variant) {
      case 'pills':
        return cn(
          baseClasses,
          item.active && 'font-medium',
          !item.disabled && 'hover:bg-opacity-50'
        );
      case 'underline':
        return cn(
          baseClasses,
          'border-b-2 border-transparent',
          item.active && 'border-current font-medium',
          !item.disabled && 'hover:border-current hover:border-opacity-50'
        );
      case 'minimal':
        return cn(
          baseClasses,
          item.active && 'font-medium',
          !item.disabled && 'hover:opacity-80'
        );
      default:
        return cn(
          baseClasses,
          item.active && 'font-medium',
          !item.disabled && 'hover:bg-opacity-50'
        );
    }
  };

  const getItemStyle = (item: NavigationMenuItem) => {
    if (item.disabled) {
      return { color: colors.mutedForeground };
    }

    if (item.active) {
      return {
        color: colors.primary,
        backgroundColor: variant === 'pills' ? colors.primary + '15' : 'transparent',
      };
    }

    return {
      color: colors.text,
      backgroundColor: 'transparent',
    };
  };

  const handleItemClick = (item: NavigationMenuItem) => {
    if (item.disabled) return;

    if (item.children && item.children.length > 0 && collapsible) {
      setExpandedItems(prev => {
        const newSet = new Set(prev);
        if (newSet.has(item.id)) {
          newSet.delete(item.id);
        } else {
          newSet.add(item.id);
        }
        return newSet;
      });
    }

    if (item.onClick) {
      item.onClick();
    }

    onItemClick?.(item);
  };

  const renderBadge = (badge: string | number) => (
    <span
      className="ml-2 px-1.5 py-0.5 text-xs font-medium rounded-full"
      style={{
        backgroundColor: colors.primary,
        color: colors.primaryForeground,
      }}
    >
      {badge}
    </span>
  );

  const renderItem = (item: NavigationMenuItem, level = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.has(item.id);

    return (
      <div key={item.id}>
        <div
          className={getItemClasses(item, level)}
          style={getItemStyle(item)}
          onClick={() => handleItemClick(item)}
          onMouseEnter={e => {
            if (!item.disabled && !item.active) {
              e.currentTarget.style.backgroundColor = colors.hover + '15';
            }
          }}
          onMouseLeave={e => {
            if (!item.disabled && !item.active) {
              e.currentTarget.style.backgroundColor = 'transparent';
            }
          }}
        >
          <div className="flex items-center">
            {showIcons && item.icon && (
              <span className="mr-2 flex-shrink-0">{item.icon}</span>
            )}
            <span>{item.label}</span>
            {item.badge && renderBadge(item.badge)}
          </div>

          {hasChildren && collapsible && (
            <span className="ml-2 transition-transform duration-200">
              {isExpanded ? '▼' : '▶'}
            </span>
          )}
        </div>

        {hasChildren && (collapsible ? isExpanded : true) && (
          <div className={orientation === 'vertical' ? 'mt-1' : 'ml-4'}>
            {item.children!.map(child => renderItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <nav
      className={cn(orientationClasses[orientation], className)}
      data-testid={testId}
    >
      {items.map(item => renderItem(item))}
    </nav>
  );
};
