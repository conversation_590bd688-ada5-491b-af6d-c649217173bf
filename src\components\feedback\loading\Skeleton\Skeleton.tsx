import React from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../utils/cn';

export interface SkeletonProps {
  variant?: 'text' | 'rectangular' | 'circular' | 'rounded';
  width?: string | number;
  height?: string | number;
  lines?: number; // For text variant
  animated?: boolean;
  className?: string;
  'data-testid'?: string;
}

/**
 * Skeleton component for loading placeholders
 * Provides visual placeholders while content is loading
 */
export const Skeleton: React.FC<SkeletonProps> = ({
  variant = 'text',
  width,
  height,
  lines = 1,
  animated = true,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const baseClasses = cn(
    'bg-gray-200 dark:bg-gray-700',
    animated && 'animate-pulse',
    className
  );

  const getVariantClasses = () => {
    switch (variant) {
      case 'text':
        return 'rounded';
      case 'rectangular':
        return '';
      case 'circular':
        return 'rounded-full';
      case 'rounded':
        return 'rounded-lg';
      default:
        return 'rounded';
    }
  };

  const getDefaultDimensions = () => {
    switch (variant) {
      case 'text':
        return { width: '100%', height: '1em' };
      case 'circular':
        return { width: '40px', height: '40px' };
      case 'rectangular':
      case 'rounded':
        return { width: '100%', height: '200px' };
      default:
        return { width: '100%', height: '1em' };
    }
  };

  const defaultDimensions = getDefaultDimensions();
  const finalWidth = width ?? defaultDimensions.width;
  const finalHeight = height ?? defaultDimensions.height;

  const skeletonStyle = {
    width: typeof finalWidth === 'number' ? `${finalWidth}px` : finalWidth,
    height: typeof finalHeight === 'number' ? `${finalHeight}px` : finalHeight,
    backgroundColor: colors.surface,
  };

  // For text variant with multiple lines
  if (variant === 'text' && lines > 1) {
    return (
      <div className="space-y-2" data-testid={testId}>
        {Array.from({ length: lines }, (_, index) => (
          <div
            key={index}
            className={cn(baseClasses, getVariantClasses())}
            style={{
              ...skeletonStyle,
              // Make last line shorter for more realistic appearance
              width: index === lines - 1 ? '75%' : skeletonStyle.width,
            }}
          />
        ))}
      </div>
    );
  }

  return (
    <div
      className={cn(baseClasses, getVariantClasses())}
      style={skeletonStyle}
      data-testid={testId}
    />
  );
};
