import React from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../utils/cn';

export interface BreadcrumbItem {
  label: string;
  href?: string;
  onClick?: () => void;
  disabled?: boolean;
}

export interface BreadcrumbProps {
  items: BreadcrumbItem[];
  separator?: React.ReactNode;
  maxItems?: number;
  showHome?: boolean;
  homeIcon?: React.ReactNode;
  className?: string;
  'data-testid'?: string;
}

export const Breadcrumb: React.FC<BreadcrumbProps> = ({
  items,
  separator = '/',
  maxItems = 5,
  showHome = false,
  homeIcon = '🏠',
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  // Truncate items if they exceed maxItems
  const displayItems = items.length > maxItems 
    ? [
        ...items.slice(0, 1),
        { label: '...', disabled: true },
        ...items.slice(-(maxItems - 2))
      ]
    : items;

  const renderItem = (item: BreadcrumbItem, index: number, isLast: boolean) => {
    const isClickable = !item.disabled && (item.href || item.onClick);
    
    const itemContent = (
      <span
        className={cn(
          'transition-colors duration-150',
          isClickable && 'hover:underline cursor-pointer',
          item.disabled && 'cursor-not-allowed opacity-50',
          isLast ? 'font-medium' : 'font-normal'
        )}
        style={{
          color: isLast ? colors.text : colors.mutedForeground,
        }}
        onClick={item.disabled ? undefined : item.onClick}
      >
        {item.label}
      </span>
    );

    if (item.href && !item.disabled) {
      return (
        <a
          key={index}
          href={item.href}
          className="no-underline"
          style={{ color: 'inherit' }}
        >
          {itemContent}
        </a>
      );
    }

    return <span key={index}>{itemContent}</span>;
  };

  const renderSeparator = (index: number) => (
    <span
      key={`separator-${index}`}
      className="mx-2 select-none"
      style={{ color: colors.mutedForeground }}
    >
      {separator}
    </span>
  );

  return (
    <nav
      className={cn('flex items-center text-sm', className)}
      aria-label="Breadcrumb"
      data-testid={testId}
    >
      {showHome && (
        <>
          <span
            className="mr-2 cursor-pointer hover:opacity-80 transition-opacity"
            style={{ color: colors.mutedForeground }}
          >
            {homeIcon}
          </span>
          {displayItems.length > 0 && renderSeparator(-1)}
        </>
      )}
      
      {displayItems.map((item, index) => {
        const isLast = index === displayItems.length - 1;
        
        return (
          <React.Fragment key={index}>
            {renderItem(item, index, isLast)}
            {!isLast && renderSeparator(index)}
          </React.Fragment>
        );
      })}
    </nav>
  );
};
