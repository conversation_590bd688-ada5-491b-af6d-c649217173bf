import React from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../utils/cn';

export interface SpinnerProps {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  color?: string;
  thickness?: number;
  speed?: 'slow' | 'normal' | 'fast';
  label?: string;
  className?: string;
  'data-testid'?: string;
}

export const Spinner: React.FC<SpinnerProps> = ({
  size = 'md',
  color,
  thickness = 2,
  speed = 'normal',
  label,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const sizeClasses = {
    xs: 'w-3 h-3',
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12',
  };

  const speedClasses = {
    slow: 'animate-spin-slow',
    normal: 'animate-spin',
    fast: 'animate-spin-fast',
  };

  const spinnerColor = color || colors.primary;

  return (
    <div
      className={cn('inline-flex items-center justify-center', className)}
      data-testid={testId}
    >
      <div
        className={cn(
          'border-solid border-transparent rounded-full',
          sizeClasses[size],
          speedClasses[speed]
        )}
        style={{
          borderWidth: thickness,
          borderTopColor: spinnerColor,
          borderRightColor: spinnerColor + '30',
        }}
        role="status"
        aria-label={label || 'Loading'}
      />
      {label && (
        <span className="ml-2 text-sm" style={{ color: colors.text }}>
          {label}
        </span>
      )}
    </div>
  );
};
