import React from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../utils/cn';

export interface StatsCardProps {
  title: string;
  value: string | number;
  change?: {
    value: number;
    type: 'increase' | 'decrease';
    period?: string;
  };
  icon?: React.ReactNode;
  variant?: 'default' | 'primary' | 'success' | 'warning' | 'error';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  hoverable?: boolean;
  clickable?: boolean;
  onClick?: (event: React.MouseEvent<HTMLDivElement>) => void;
  className?: string;
  'data-testid'?: string;
}

export const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  change,
  icon,
  variant = 'default',
  size = 'md',
  loading = false,
  hoverable = false,
  clickable = false,
  onClick,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const sizeClasses = {
    sm: 'p-3',
    md: 'p-4',
    lg: 'p-6',
  };

  const valueSizes = {
    sm: 'text-xl',
    md: 'text-2xl',
    lg: 'text-3xl',
  };

  const titleSizes = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
  };

  const variantStyles = {
    default: {
      backgroundColor: colors.surface,
      borderColor: colors.border,
      iconColor: colors.primary,
    },
    primary: {
      backgroundColor: colors.primary + '10',
      borderColor: colors.primary,
      iconColor: colors.primary,
    },
    success: {
      backgroundColor: '#10b98110',
      borderColor: '#10b981',
      iconColor: '#10b981',
    },
    warning: {
      backgroundColor: '#f59e0b10',
      borderColor: '#f59e0b',
      iconColor: '#f59e0b',
    },
    error: {
      backgroundColor: '#ef444410',
      borderColor: '#ef4444',
      iconColor: '#ef4444',
    },
  };

  const currentStyle = variantStyles[variant];

  const getChangeColor = () => {
    if (!change) return colors.textSecondary;
    return change.type === 'increase' ? '#10b981' : '#ef4444';
  };

  const getChangeIcon = () => {
    if (!change) return null;
    return change.type === 'increase' ? '↗' : '↘';
  };

  if (loading) {
    return (
      <div
        className={cn(
          'rounded-lg border animate-pulse',
          sizeClasses[size],
          className
        )}
        style={{
          backgroundColor: colors.surface,
          borderColor: colors.border,
        }}
        data-testid={testId}
      >
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div
              className="h-4 rounded"
              style={{ backgroundColor: colors.muted, width: '60%' }}
            />
            {icon && (
              <div
                className="w-8 h-8 rounded"
                style={{ backgroundColor: colors.muted }}
              />
            )}
          </div>
          <div
            className="h-8 rounded"
            style={{ backgroundColor: colors.muted, width: '40%' }}
          />
          <div
            className="h-3 rounded"
            style={{ backgroundColor: colors.muted, width: '50%' }}
          />
        </div>
      </div>
    );
  }

  return (
    <div
      className={cn(
        'rounded-lg border transition-all duration-200',
        sizeClasses[size],
        hoverable && 'hover:shadow-md hover:scale-[1.02]',
        clickable && 'cursor-pointer',
        className
      )}
      style={{
        backgroundColor: currentStyle.backgroundColor,
        borderColor: currentStyle.borderColor,
      }}
      onClick={clickable ? onClick : undefined}
      data-testid={testId}
    >
      <div className="space-y-2">
        {/* Header */}
        <div className="flex items-center justify-between">
          <h3
            className={cn('font-medium uppercase tracking-wide', titleSizes[size])}
            style={{ color: colors.textSecondary }}
          >
            {title}
          </h3>
          
          {icon && (
            <div
              className="p-2 rounded-lg"
              style={{
                backgroundColor: currentStyle.iconColor + '20',
                color: currentStyle.iconColor,
              }}
            >
              {icon}
            </div>
          )}
        </div>

        {/* Value */}
        <div
          className={cn('font-bold', valueSizes[size])}
          style={{ color: colors.text }}
        >
          {value}
        </div>

        {/* Change indicator */}
        {change && (
          <div className="flex items-center space-x-1">
            <span
              className="text-sm font-medium flex items-center"
              style={{ color: getChangeColor() }}
            >
              <span className="mr-1">{getChangeIcon()}</span>
              {Math.abs(change.value)}%
            </span>
            {change.period && (
              <span
                className="text-sm"
                style={{ color: colors.textSecondary }}
              >
                {change.period}
              </span>
            )}
          </div>
        )}
      </div>
    </div>
  );
};
