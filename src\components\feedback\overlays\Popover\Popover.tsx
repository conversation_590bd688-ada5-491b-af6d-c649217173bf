import React, { useRef, useEffect, useState } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../utils/cn';

export interface PopoverProps {
  children: React.ReactNode;
  content: React.ReactNode;
  trigger?: 'click' | 'hover' | 'focus';
  placement?: 'top' | 'bottom' | 'left' | 'right' | 'top-start' | 'top-end' | 'bottom-start' | 'bottom-end';
  offset?: number;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  disabled?: boolean;
  className?: string;
  contentClassName?: string;
  'data-testid'?: string;
}

/**
 * Popover component for contextual content overlays
 * Provides floating content positioned relative to a trigger element
 */
export const Popover: React.FC<PopoverProps> = ({
  children,
  content,
  trigger = 'click',
  placement = 'bottom',
  offset = 8,
  open: controlledOpen,
  onOpenChange,
  disabled = false,
  className = '',
  contentClassName = '',
  'data-testid': testId,
}) => {
  const [internalOpen, setInternalOpen] = useState(false);
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const triggerRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const { colors } = useThemeStore();

  const isOpen = controlledOpen !== undefined ? controlledOpen : internalOpen;

  const updatePosition = () => {
    if (!triggerRef.current || !contentRef.current) return;

    const triggerRect = triggerRef.current.getBoundingClientRect();
    const contentRect = contentRef.current.getBoundingClientRect();
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight,
    };

    let top = 0;
    let left = 0;

    switch (placement) {
      case 'top':
        top = triggerRect.top - contentRect.height - offset;
        left = triggerRect.left + (triggerRect.width - contentRect.width) / 2;
        break;
      case 'bottom':
        top = triggerRect.bottom + offset;
        left = triggerRect.left + (triggerRect.width - contentRect.width) / 2;
        break;
      case 'left':
        top = triggerRect.top + (triggerRect.height - contentRect.height) / 2;
        left = triggerRect.left - contentRect.width - offset;
        break;
      case 'right':
        top = triggerRect.top + (triggerRect.height - contentRect.height) / 2;
        left = triggerRect.right + offset;
        break;
      case 'top-start':
        top = triggerRect.top - contentRect.height - offset;
        left = triggerRect.left;
        break;
      case 'top-end':
        top = triggerRect.top - contentRect.height - offset;
        left = triggerRect.right - contentRect.width;
        break;
      case 'bottom-start':
        top = triggerRect.bottom + offset;
        left = triggerRect.left;
        break;
      case 'bottom-end':
        top = triggerRect.bottom + offset;
        left = triggerRect.right - contentRect.width;
        break;
    }

    // Keep popover within viewport
    if (left < 0) left = 8;
    if (left + contentRect.width > viewport.width) left = viewport.width - contentRect.width - 8;
    if (top < 0) top = 8;
    if (top + contentRect.height > viewport.height) top = viewport.height - contentRect.height - 8;

    setPosition({ top, left });
  };

  useEffect(() => {
    if (isOpen) {
      updatePosition();
      window.addEventListener('resize', updatePosition);
      window.addEventListener('scroll', updatePosition);
      
      return () => {
        window.removeEventListener('resize', updatePosition);
        window.removeEventListener('scroll', updatePosition);
      };
    }
  }, [isOpen, placement, offset]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        isOpen &&
        triggerRef.current &&
        contentRef.current &&
        !triggerRef.current.contains(event.target as Node) &&
        !contentRef.current.contains(event.target as Node)
      ) {
        handleOpenChange(false);
      }
    };

    if (trigger === 'click') {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen, trigger]);

  const handleOpenChange = (newOpen: boolean) => {
    if (disabled) return;
    
    if (onOpenChange) {
      onOpenChange(newOpen);
    } else {
      setInternalOpen(newOpen);
    }
  };

  const handleTriggerClick = () => {
    if (trigger === 'click') {
      handleOpenChange(!isOpen);
    }
  };

  const handleTriggerMouseEnter = () => {
    if (trigger === 'hover') {
      handleOpenChange(true);
    }
  };

  const handleTriggerMouseLeave = () => {
    if (trigger === 'hover') {
      handleOpenChange(false);
    }
  };

  const handleTriggerFocus = () => {
    if (trigger === 'focus') {
      handleOpenChange(true);
    }
  };

  const handleTriggerBlur = () => {
    if (trigger === 'focus') {
      handleOpenChange(false);
    }
  };

  return (
    <>
      <div
        ref={triggerRef}
        className={cn('inline-block', className)}
        onClick={handleTriggerClick}
        onMouseEnter={handleTriggerMouseEnter}
        onMouseLeave={handleTriggerMouseLeave}
        onFocus={handleTriggerFocus}
        onBlur={handleTriggerBlur}
        data-testid={testId}
      >
        {children}
      </div>

      {isOpen && (
        <div
          ref={contentRef}
          className={cn(
            'fixed z-50 rounded-lg shadow-lg border',
            'animate-in fade-in-0 zoom-in-95 duration-200',
            contentClassName
          )}
          style={{
            top: position.top,
            left: position.left,
            backgroundColor: colors.surface,
            borderColor: colors.border,
          }}
        >
          {content}
        </div>
      )}
    </>
  );
};
