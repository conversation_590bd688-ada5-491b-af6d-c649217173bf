import React from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../utils/cn';

export interface TimelineItemProps {
  children: React.ReactNode;
  title?: string;
  timestamp?: string | Date;
  icon?: React.ReactNode;
  variant?: 'default' | 'success' | 'warning' | 'error' | 'info';
  size?: 'sm' | 'md' | 'lg';
  active?: boolean;
  last?: boolean;
  className?: string;
  'data-testid'?: string;
}

export const TimelineItem: React.FC<TimelineItemProps> = ({
  children,
  title,
  timestamp,
  icon,
  variant = 'default',
  size = 'md',
  active = false,
  last = false,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const sizeClasses = {
    sm: 'pl-8',
    md: 'pl-10',
    lg: 'pl-12',
  };

  const dotSizes = {
    sm: 'w-2 h-2',
    md: 'w-3 h-3',
    lg: 'w-4 h-4',
  };

  const iconSizes = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-10 h-10',
  };

  const variantColors = {
    default: colors.primary,
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
    info: '#3b82f6',
  };

  const currentColor = variantColors[variant];

  const formatTimestamp = (ts: string | Date) => {
    if (ts instanceof Date) {
      return ts.toLocaleString();
    }
    return ts;
  };

  return (
    <div
      className={cn(
        'relative',
        sizeClasses[size],
        className
      )}
      data-testid={testId}
    >
      {/* Timeline dot/icon */}
      <div
        className={cn(
          'absolute left-0 flex items-center justify-center rounded-full border-2',
          icon ? iconSizes[size] : dotSizes[size],
          active && 'ring-2 ring-opacity-50'
        )}
        style={{
          backgroundColor: icon ? colors.surface : currentColor,
          borderColor: currentColor,
          color: icon ? currentColor : colors.surface,
          ringColor: active ? currentColor : 'transparent',
          top: size === 'sm' ? '2px' : size === 'md' ? '4px' : '6px',
        }}
      >
        {icon || <div className="w-full h-full rounded-full" />}
      </div>

      {/* Content */}
      <div className="space-y-1">
        {/* Header */}
        {(title || timestamp) && (
          <div className="flex items-center justify-between">
            {title && (
              <h4
                className="font-medium"
                style={{ color: colors.text }}
              >
                {title}
              </h4>
            )}
            
            {timestamp && (
              <time
                className="text-sm"
                style={{ color: colors.textSecondary }}
              >
                {formatTimestamp(timestamp)}
              </time>
            )}
          </div>
        )}

        {/* Content */}
        <div style={{ color: colors.textSecondary }}>
          {children}
        </div>
      </div>
    </div>
  );
};
