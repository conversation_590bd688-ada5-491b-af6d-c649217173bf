import React from 'react';
import { cn } from '../../../../utils/cn';
import type { BaseLayoutProps } from '../../../types/layout';

export interface CenterProps extends BaseLayoutProps {
  children: React.ReactNode;
  inline?: boolean;
  fullHeight?: boolean;
}

/**
 * Center component for centering content both horizontally and vertically
 * Provides flexible centering options for different use cases
 */
export const Center: React.FC<CenterProps> = ({
  children,
  inline = false,
  fullHeight = false,
  className = '',
  'data-testid': testId,
}) => {
  const centerClasses = cn(
    inline ? 'inline-flex' : 'flex',
    'items-center justify-center',
    fullHeight && 'min-h-full',
    className
  );

  return (
    <div className={centerClasses} data-testid={testId}>
      {children}
    </div>
  );
};
