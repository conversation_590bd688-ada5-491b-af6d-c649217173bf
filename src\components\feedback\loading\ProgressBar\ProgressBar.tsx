import React from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../utils/cn';

export interface ProgressBarProps {
  value: number; // 0-100
  max?: number;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'success' | 'warning' | 'error';
  showLabel?: boolean;
  label?: string;
  animated?: boolean;
  striped?: boolean;
  className?: string;
  'data-testid'?: string;
}

export const ProgressBar: React.FC<ProgressBarProps> = ({
  value,
  max = 100,
  size = 'md',
  variant = 'default',
  showLabel = false,
  label,
  animated = false,
  striped = false,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const percentage = Math.min(Math.max((value / max) * 100, 0), 100);

  const sizeClasses = {
    sm: 'h-1',
    md: 'h-2',
    lg: 'h-3',
  };

  const variantColors = {
    default: colors.primary,
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
  };

  const progressColor = variantColors[variant];

  return (
    <div className={cn('w-full', className)} data-testid={testId}>
      {(showLabel || label) && (
        <div className="flex justify-between items-center mb-1">
          <span className="text-sm font-medium" style={{ color: colors.text }}>
            {label || `${Math.round(percentage)}%`}
          </span>
          {showLabel && !label && (
            <span className="text-sm" style={{ color: colors.mutedForeground }}>
              {value}/{max}
            </span>
          )}
        </div>
      )}
      
      <div
        className={cn(
          'w-full bg-gray-200 rounded-full overflow-hidden',
          sizeClasses[size]
        )}
        style={{ backgroundColor: colors.border }}
      >
        <div
          className={cn(
            'h-full transition-all duration-300 ease-out',
            striped && 'bg-stripes',
            animated && striped && 'animate-stripes'
          )}
          style={{
            width: `${percentage}%`,
            backgroundColor: progressColor,
            backgroundImage: striped
              ? `linear-gradient(45deg, rgba(255,255,255,0.15) 25%, transparent 25%, transparent 50%, rgba(255,255,255,0.15) 50%, rgba(255,255,255,0.15) 75%, transparent 75%, transparent)`
              : undefined,
            backgroundSize: striped ? '1rem 1rem' : undefined,
          }}
          role="progressbar"
          aria-valuenow={value}
          aria-valuemin={0}
          aria-valuemax={max}
        />
      </div>
    </div>
  );
};
