import React from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { cn } from '../../../../utils/cn';

export interface PageInfoProps {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalItems: number;
  variant?: 'default' | 'compact' | 'detailed';
  className?: string;
  'data-testid'?: string;
}

export const PageInfo: React.FC<PageInfoProps> = ({
  currentPage,
  totalPages,
  pageSize,
  totalItems,
  variant = 'default',
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const startItem = Math.min((currentPage - 1) * pageSize + 1, totalItems);
  const endItem = Math.min(currentPage * pageSize, totalItems);

  const renderContent = () => {
    switch (variant) {
      case 'compact':
        return `${currentPage} of ${totalPages}`;
      
      case 'detailed':
        return (
          <div className="space-y-1">
            <div>
              Showing {startItem} to {endItem} of {totalItems} results
            </div>
            <div className="text-xs opacity-75">
              Page {currentPage} of {totalPages}
            </div>
          </div>
        );
      
      case 'default':
      default:
        return `Showing ${startItem} to ${endItem} of ${totalItems} results`;
    }
  };

  return (
    <div
      className={cn(
        'text-sm',
        variant === 'detailed' ? 'text-left' : 'text-center',
        className
      )}
      style={{ color: colors.textSecondary }}
      data-testid={testId}
    >
      {renderContent()}
    </div>
  );
};
